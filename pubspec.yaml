name: aj<PERSON>_now_doctor
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.13+14

environment:
  sdk: '>=3.0.0 <4.0.0'


# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_dotenv: ^5.0.2
  intl: 0.19.0
  flutter_rating_bar: ^4.0.1
  go_router: ^16.0.0
  flutter_riverpod: ^2.0.0-dev.9
  path_provider: ^2.0.11
  google_fonts: ^6.2.1
  auto_size_text: ^3.0.0
  package_info_plus: any
  carousel_slider: ^5.1.1
  permission_handler: ^12.0.1
  fzregex: ^2.0.0
  dropdown_button2: ^2.3.9
  qr_code_scanner_plus: ^2.0.10

  flutter_local_notifications:
  flutter_fgbg: ^0.7.1
  flutter_new_badger: ^1.1.0

  sqflite: ^2.2.0+3
  mutex: ^3.0.0
  shared_preferences: ^2.0.15
  rx_shared_preferences: ^4.0.0
  connectivity_plus: ^6.1.4
  dio: ^5.8.0+1

  map_launcher: ^3.5.0
  flutter_svg: ^2.2.0
  google_maps_flutter: ^2.5.3
  geolocator: ^10.0.0
  bubble: ^1.2.1
  qr_flutter: ^4.0.0
  image_picker: ^1.1.2
  flutter_image_compress: ^2.4.0

  # For Firebase
  firebase_auth: ^5.6.2
  firebase_core: ^3.15.1
  firebase_messaging: ^15.2.9
  firebase_analytics: ^11.5.2
  firebase_remote_config: ^5.4.7

  flutter_vector_icons: ^2.0.0
  pin_code_fields: ^8.0.1
  galleryimage: ^2.0.1
  table_calendar: ^3.0.7

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  time_picker_spinner: ^0.0.1
  dropdown_textfield: ^1.0.8
  image_cropper: ^9.1.0
  smooth_page_indicator: ^1.2.0+3
  mime: ^1.0.4
  http_parser: ^4.0.2
  geocoding: ^3.0.0
  url_launcher: ^6.3.2
  cached_network_image: ^3.3.1
  flutter_launcher_icons: ^0.14.4

  json_annotation: ^4.9.0


dependency_overrides:
  watcher: 1.1.0


dev_dependencies:
  flutter_test:
    sdk: flutter
  intl_utils: ^2.8.1
  build_runner: ^2.5.4
  freezed: ^2.2.1
  freezed_annotation: ^2.4.4
  json_serializable: 6.8.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

flutter_icons:
  android: true
  ios: true
  image_path: "assets/Logo.png"


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - lib/.env
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true
