import 'dart:convert';

import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/domain/schedule/schedule.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/common/general_widgets/no_data.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/calender/data/calender_repository.dart';
import 'package:ajmal_now_doctor/features/calender/presentation/reservation_card.dart';
import 'package:ajmal_now_doctor/features/calender/presentation/table_calender.dart';
import 'package:ajmal_now_doctor/features/statistics/data/service/service_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/services/services.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/utils/datetime_extensions.dart';
import 'package:ajmal_now_doctor/utils/string_extensions.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

enum ReservationStatus { upcoming, complete, cancelled, blocked }

extension HistoryStatusOperations on ReservationStatus {
  String get name {
    switch (this) {
      case ReservationStatus.upcoming:
        return 'Upcoming'.hardcoded;
      case ReservationStatus.complete:
        return 'Complete'.hardcoded;
      case ReservationStatus.cancelled:
        return 'Cancelled'.hardcoded;
      case ReservationStatus.blocked:
        return 'Blocked'.hardcoded;
    }
  }

  Color get cardColor {
    switch (this) {
      case ReservationStatus.upcoming:
        return Colors.white;
      case ReservationStatus.complete:
        return Colors.white;
      case ReservationStatus.cancelled:
        return const Color(0xFFE5E5E5);
      case ReservationStatus.blocked:
        return const Color(0xFFE5E5E5);
    }
  }

  Color get textColor {
    switch (this) {
      case ReservationStatus.upcoming:
        return Colors.white;
      case ReservationStatus.complete:
        return Colors.white;
      case ReservationStatus.cancelled:
        return const Color(0xFFA3A3A3);
      case ReservationStatus.blocked:
        return const Color(0xFFA3A3A3);
    }
  }
}

class ANCalenderScreen extends ConsumerStatefulWidget {
  const ANCalenderScreen({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState createState() => _ANCalenderScreenState();
}

class _ANCalenderScreenState extends ConsumerState<ANCalenderScreen> with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final serviceProvidersController = ref.watch(servicesProvider);
    final serviceProvidersData = ref.watch(servicesDataProvider);

    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableBottomNavBar: false,
        disableAppbar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      appbar: ANMainScreenAppbar(
        scaffoldKey: _scaffoldKey,
        centerTitle: true,
        title: Text(
          S.of(context).kCalender,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Branch selection dropdown
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  S.of(context).kMyBranches,
                  style: GoogleFonts.montserrat(
                      fontSize: 12.0, fontWeight: FontWeight.w300),
                  textAlign: TextAlign.start,
                ),
                gapHS,
                serviceProvidersData.when(
                  data: (data) {
                    if (data.isEmpty) {
                      return ANNoData(
                        message: S
                            .of(context)
                            .kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices,
                        additionalContent: ANElevatedButton(
                            onPressed: () {},
                            child: Text(
                              S.of(context).kContactUs,
                              style: const TextStyle(
                                color: Colors.white,
                              ),
                            )),
                      );
                    } else {
                      return DropdownButtonHideUnderline(
                        child: DropdownButton2<ANServiceModel>(
                          value: serviceProvidersController.selectedService,
                          hint: Text(S.of(context).kSelectAServiceToViewItsDetails),
                          buttonStyleData: ButtonStyleData(
                            decoration: BoxDecoration(
                              borderRadius: buttonBorderRadius,
                              color: Colors.white,
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 20.0),
                            width: MediaQuery.of(context).size.width,
                            elevation: 0,
                          ),
                          dropdownStyleData: DropdownStyleData(
                            decoration: BoxDecoration(
                              borderRadius: buttonBorderRadius,
                            ),
                            padding: EdgeInsets.zero,
                          ),
                          menuItemStyleData: const MenuItemStyleData(
                            overlayColor: MaterialStatePropertyAll(AppColors.primaryColor),
                          ),
                          selectedItemBuilder: (_) => data
                              .map((e) => Center(child: Text(e.name)))
                              .toList(),
                          items: data
                              .map((e) => DropdownMenuItem<ANServiceModel>(
                                  value: e,
                                  child: Text(
                                    e.name,
                                    style: TextStyle(
                                        color: serviceProvidersController.selectedService == e
                                            ? Colors.white
                                            : Colors.black),
                                  )))
                              .toList(),
                          onChanged: (value) {
                            if (value != null) {
                              serviceProvidersController.selectService(value);
                              // Refresh both tab data
                              if (value.id != null) {
                                ref.refresh(upcomingAppointmentsProvider(value.id!));
                                ref.refresh(pastAppointmentsProvider(value.id!));
                              }
                            }
                          },
                        ),
                      );
                    }
                  },
                  error: (error, _) => Center(
                    child: ANError(
                      errorMessage: error.toString(),
                      refreshCallback: () async {},
                    ),
                  ),
                  loading: () => const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.0),
                      child: CircularProgressIndicator(color: AppColors.primaryColor),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Show the tabs and appointment lists only if a branch is selected
          if (serviceProvidersController.selectedService != null)
            Expanded(
              child: Column(
                children: [
                  gapHL,
                  // Tab Bar
                  TabBar(
                    controller: _tabController,
                    labelColor: AppColors.primaryColor,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppColors.primaryColor,
                    indicatorWeight: 3,
                    tabs: [
                      Tab(text: S.of(context).kUpcoming),
                      Tab(text: S.of(context).kPast),
                    ],
                  ),

                  // Tab Bar View
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Upcoming Appointments Tab
                        _buildAppointmentsList(
                          isUpcoming: true,
                          branchId: serviceProvidersController.selectedService!.id!
                        ),

                        // Past Appointments Tab
                        _buildAppointmentsList(
                          isUpcoming: false,
                          branchId: serviceProvidersController.selectedService!.id!
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAppointmentsList({required bool isUpcoming, required String branchId}) {
    final appointmentsProvider = isUpcoming
        ? upcomingAppointmentsProvider(branchId)
        : pastAppointmentsProvider(branchId);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Consumer(
        builder: (context, ref, child) {
          return ref.watch(appointmentsProvider).when(
            data: (appointments) {
              if (appointments.isEmpty) {
                return Center(
                  child: Text(
                    isUpcoming
                        ? S.of(context).kNoUpcomingAppointments
                        : S.of(context).kNoPastAppointments,
                    style: TextSize.xs.regularStyle,
                  ),
                );
              }

              return ListView.separated(
                itemCount: appointments.length,
                itemBuilder: (BuildContext context, int index) {
                  final appointment = appointments[index];
                  return ANAppointmentCard(
                    appointment: appointment,
                    callback: () {
                      // Refresh the list after any action
                      ref.refresh(appointmentsProvider);
                    },
                  );
                },
                separatorBuilder: (BuildContext context, int index) => gapHS,
              );
            },
            error: (error, stackTrace) {
              return Center(
                child: ANError(
                  errorMessage: error.toString(),
                  refreshCallback: () async {
                    ref.refresh(appointmentsProvider);
                  },
                ),
              );
            },
            loading: () => const Center(
              child: CircularProgressIndicator(color: AppColors.primaryColor),
            ),
          );
        },
      ),
    );
  }
}
