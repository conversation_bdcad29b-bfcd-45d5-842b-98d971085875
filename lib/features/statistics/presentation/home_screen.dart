import 'package:ajmal_now_doctor/common/base_widgets/main_screen_appbar.dart';
import 'package:ajmal_now_doctor/common/base_widgets/scaffold.dart';
import 'package:ajmal_now_doctor/common/buttons/elevated_button.dart';
import 'package:ajmal_now_doctor/common/general_widgets/error.dart';
import 'package:ajmal_now_doctor/common/general_widgets/no_data.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/statistics/data/service/service_repository.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/notification_permission_handler.dart';
import 'package:ajmal_now_doctor/generated/l10n.dart';
import 'package:ajmal_now_doctor/utils/paged_data/paged_data.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';

class ANHomeScreen extends ConsumerStatefulWidget {
  const ANHomeScreen({Key? key}) : super(key: key);

  @override
  ConsumerState createState() => _ANHomeScreenState();
}

class _ANHomeScreenState extends ConsumerState<ANHomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    NotificationPermissionHandler.requestNotificationPermission();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authController = ref.watch(authRepositoryProvider);
    final servicesController = ref.watch(servicesProvider);
    // final servicesData = ref.watch(servicesDataProvider);
    final serviceProviderStatistics =
        ref.watch(serviceProviderStatisticsByIdProvider);

    return ANScaffold(
      scaffoldKey: _scaffoldKey,
      scaffoldOptions: const ANScaffoldOptions(
        disableAppbar: false,
        disableBottomNavBar: false,
        disableDefaultPaddingVertically: true,
        disableWillPopScope: false,
      ),
      appbar: ANMainScreenAppbar(
        scaffoldKey: _scaffoldKey,
        centerTitle: false,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              S.of(context).kWelcomeBack,
              style: authController.currentUser != null
                  ? TextSize.xs.mediumStyle.copyWith(color: Colors.black)
                  : TextSize.l.boldStyle.copyWith(color: Colors.black),
            ),
            if (authController.currentUser != null)
              Text(
                '${authController.currentUser!.firstName} ${authController.currentUser!.lastName}',
                style: TextSize.l.boldStyle.copyWith(color: Colors.black),
              )
          ],
        ),
      ),
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () async {
          setState(() {
            servicesController.selectedService = null;
          });
          await ref.refresh(servicesDataProvider.future);
        },
        child: SingleChildScrollView(
          controller: scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                S.of(context).kMyBranches,
                style: GoogleFonts.montserrat(
                    fontSize: 12.0, fontWeight: FontWeight.w300),
              ),
              gapHS,
              // servicesData.when(
              //   data: (data) {
              //     if (data.isEmpty) {
              //       return ANNoData(
              //         message: S
              //             .of(context)
              //             .kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices,
              //         additionalContent: ANElevatedButton(
              //           onPressed: () {},
              //           child: Text(S.of(context).kContactUs),
              //         ),
              //       );
              //     } else {
              //       return DropdownButtonHideUnderline(
              //         child: DropdownButton2<ANServiceModel>(
              //           value: servicesController.selectedService,
              //           hint:
              //               Text(S.of(context).kSelectAServiceToViewItsDetails),
              //           buttonDecoration: BoxDecoration(
              //             borderRadius: buttonBorderRadius,
              //             color: Colors.white,
              //           ),
              //           buttonPadding:
              //               const EdgeInsets.symmetric(horizontal: 20.0),
              //           buttonWidth: MediaQuery.of(context).size.width,
              //           buttonElevation: 0,
              //           dropdownDecoration: BoxDecoration(
              //             borderRadius: buttonBorderRadius,
              //           ),
              //           selectedItemHighlightColor: AppColors.primaryColor,
              //           dropdownPadding: EdgeInsets.zero,
              //           selectedItemBuilder: (_) => data
              //               .map((e) => Center(child: Text(e.name)))
              //               .toList(),
              //           items: data
              //               .map((e) => DropdownMenuItem<ANServiceModel>(
              //                     value: e,
              //                     child: Text(
              //                       e.name,
              //                       style: TextStyle(
              //                           color: servicesController
              //                                       .selectedService ==
              //                                   e
              //                               ? Colors.white
              //                               : Colors.black),
              //                     ),
              //                   ))
              //               .toList(),
              //           onChanged: (value) {
              //             if (value != null) {
              //               servicesController.selectService(value);
              //               ref.refresh(
              //                   serviceProviderStatisticsByIdProvider.future);
              //             }
              //           },
              //         ),
              //       );
              //     }
              //   },
              //   error: (error, _) => Center(
              //     child: ANError(
              //       errorMessage: error.toString(),
              //       refreshCallback: () async {},
              //     ),
              //   ),
              //   loading: () => const Center(
              //     child: Padding(
              //       padding: EdgeInsets.symmetric(vertical: 10.0),
              //       child: CircularProgressIndicator(
              //         color: AppColors.primaryColor,
              //       ),
              //     ),
              //   ),
              // ),
              Consumer(
                builder: (context, ref, child) {
                  final servicesData = ref.watch(servicesDataProvider);

                  return servicesData.map(
                    data: (list) {
                      final data = list.value;
                      if (data.isEmpty) {
                        return ANNoData(
                          message: S
                              .of(context)
                              .kYouDoNotOwnAnyServicesRightNowPleaseContactUsToSubmitYourServices,
                          additionalContent: ANElevatedButton(
                            onPressed: () {},
                            child: Text(
                              S.of(context).kContactUs,
                              style: const TextStyle(
                                color: Colors.white,
                              ),
                            ),
                          ),
                        );
                      } else {
                        return DropdownButtonHideUnderline(
                          child: DropdownButton2<ANServiceModel>(
                            value: servicesController.selectedService,
                            hint: Text(
                                S.of(context).kSelectAServiceToViewItsDetails),
                            buttonStyleData: ButtonStyleData(
                              decoration: BoxDecoration(
                                borderRadius: buttonBorderRadius,
                                color: Colors.white,
                              ),
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              width: MediaQuery.of(context).size.width,
                              elevation: 0,
                            ),
                            dropdownStyleData: DropdownStyleData(
                              decoration: BoxDecoration(
                                borderRadius: buttonBorderRadius,
                              ),
                              padding: EdgeInsets.zero,
                            ),
                            menuItemStyleData: const MenuItemStyleData(
                              overlayColor: MaterialStatePropertyAll(AppColors.primaryColor),
                            ),
                            selectedItemBuilder: (_) => data
                                .map((e) => Center(child: Text(e.name)))
                                .toList(),
                            items: data
                                .map((e) => DropdownMenuItem<ANServiceModel>(
                                      value: e,
                                      child: Text(
                                        e.name,
                                        style: TextStyle(
                                            color: servicesController
                                                        .selectedService ==
                                                    e
                                                ? Colors.white
                                                : Colors.black),
                                      ),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              if (value != null) {
                                servicesController.selectService(value);
                                ref.refresh(
                                    serviceProviderStatisticsByIdProvider
                                        .future);
                              }
                            },
                          ),
                        );
                      }
                    },
                    error: (error) => Center(
                      child: ANError(
                        errorMessage: error.toString(),
                        refreshCallback: () async {},
                      ),
                    ),
                    loading: (_) => const Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 10.0),
                        child: CircularProgressIndicator(
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                  );
                },
              ),
              if (servicesController.selectedService != null)
                serviceProviderStatistics.when(
                  skipLoadingOnRefresh: false,
                  data: (data) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Card(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 25.0, vertical: 20.0),
                          shape: RoundedRectangleBorder(
                            borderRadius: circularBorderXL,
                          ),
                          color: const Color(0xFFE7B0F5).withOpacity(0.5),
                          elevation: 0.0,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 20.0),
                            child: Column(
                              children: [
                                Text(
                                  S.of(context).kTotalBookings,
                                  style: TextSize.s.semiBoldStyle,
                                ),
                                gapHXL,
                                Card(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: circularBorderXXS,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16.0, horizontal: 24.0),
                                    child: Text(
                                      '${data.all.cancelled.length + data.all.completed.length}',
                                      style: TextSize.xl.boldStyle,
                                    ),
                                  ),
                                ),
                                gapHXXL,
                              ],
                            ),
                          ),
                        ),
                        gapHXL,
                        gapHXL,
                        Column(
                          children: [
                            Text(
                              S.of(context).kBookingsLastWeek,
                              style: TextSize.s.semiBoldStyle,
                            ),
                            gapHXL,
                            Card(
                              shape: RoundedRectangleBorder(
                                borderRadius: circularBorderXXS,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 16.0, horizontal: 24.0),
                                child: Text(
                                  '${data.lastMonth.cancelled.length + data.lastMonth.completed.length}',
                                  style: TextSize.xl.boldStyle,
                                ),
                              ),
                            ),
                            gapHXXL,
                            Text(
                              S.of(context).kBookingsThisWeek,
                              style: TextSize.s.semiBoldStyle,
                            ),
                            gapHXL,
                            Card(
                              shape: RoundedRectangleBorder(
                                borderRadius: circularBorderXXS,
                              ),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 16.0, horizontal: 24.0),
                                child: Text(
                                  '${data.thisMonth.cancelled.length + data.thisMonth.completed.length}',
                                  style: TextSize.xl.boldStyle,
                                ),
                              ),
                            ),
                            gapHXXL,
                          ],
                        ),
                      ],
                    );
                  },
                  error: (error, _) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 10.0),
                    child: Center(
                      child: ANError(
                        errorMessage: error.toString(),
                        refreshCallback: () async {},
                      ),
                    ),
                  ),
                  loading: () => const Center(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 10.0),
                      child: CircularProgressIndicator(
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class ANCategoriesFilterModel {
  String label;
  bool isSelected;

  ANCategoriesFilterModel({required this.label, this.isSelected = false});
}

class ANPaginatedList<T> extends ConsumerStatefulWidget {
  final AsyncValue<void> state;
  final FutureProvider futureProvider;
  final Widget Function(dynamic) card;
  final ANPagedDataModel data;
  final ScrollController? parentScrollController;

  const ANPaginatedList(
      {Key? key,
      required this.state,
      required this.futureProvider,
      required this.card,
      required this.data,
      this.parentScrollController})
      : super(key: key);

  @override
  ConsumerState createState() => _ANPaginatedListState();
}

class _ANPaginatedListState extends ConsumerState<ANPaginatedList> {
  late ScrollController _controller;
  String? _error;
  final ValueNotifier<bool> _fetchingData = ValueNotifier(true);

  // Future<void> _refresh() async {
  //   widget.refresh.call();
  //
  //   widget.getData.call().then((value) {
  //     Future.delayed(const Duration(milliseconds: 200), () {
  //       if (_controller.hasClients && _controller.position.atEdge) {
  //         _controller.notifyListeners();
  //       }
  //     });
  //   });
  // }

  _scrollListener() {
    if (!_fetchingData.value &&
        widget.data.hasNext() &&
        _controller.offset >= _controller.position.maxScrollExtent &&
        !_controller.position.outOfRange) {
      ref.refresh(widget.futureProvider.future);
    }
  }

  @override
  void initState() {
    print("AAAAAAAAAAAAAAAAAAAAA1");
    if (widget.parentScrollController != null) {
      print("BBBBBBBBBBBBBBBBBB1");
      _controller = widget.parentScrollController!;
    } else {
      print("CCCCCCCCCCCCCCCCCCCCC1");
      _controller = ScrollController();
    }
    _controller.addListener(_scrollListener);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.data.list.isEmpty) {
        print("DDDDDDDDDDDDDDDDDDDDDDDD1");
        _fetchingData.value = true;
        ref.refresh(widget.futureProvider.future).then((_) {
          Future.delayed(const Duration(milliseconds: 200), () {
            if (_controller.hasClients && _controller.position.atEdge) {
              print("EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE1");
              _controller.notifyListeners();
            }
          });
        });
      } else {
        print("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF1");
        _fetchingData.value = false;
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.data.list.isNotEmpty)
          ListView.separated(
            key: const PageStorageKey<String>('controller'),
            controller:
                widget.parentScrollController != null ? null : _controller,
            shrinkWrap: true,
            physics: widget.parentScrollController != null
                ? const NeverScrollableScrollPhysics()
                : const AlwaysScrollableScrollPhysics(),
            itemCount: widget.data.list.length,
            itemBuilder: (BuildContext context, int index) => widget.card(
              widget.data.list.elementAt(index),
            ),
            separatorBuilder: (BuildContext context, int index) => gapHXL,
          )
        else
          Flexible(
            child: ValueListenableBuilder(
              valueListenable: _fetchingData,
              builder: (BuildContext context, bool value, Widget? child) =>
                  value
                      ? const SizedBox()
                      // ? Center(child: Text('Loading'.hardcoded))
                      : _error != null
                          ? Center(
                              child: ANError(
                                errorMessage:
                                    _error ?? S.of(context).kNoDataToDisplay,
                                refreshCallback: () async {
                                  await ref
                                      .refresh(widget.futureProvider.future);
                                },
                              ),
                            )
                          : const ANNoData(),
            ),
          ),
        widget.state.when(
          skipLoadingOnRefresh: false,
          loading: () {
            setState(() => _fetchingData.value = true);
            return const Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 10.0),
                child: CircularProgressIndicator(color: AppColors.primaryColor),
              ),
            );
          },
          error: (error, stackTrace) {
            print(stackTrace.toString());
            setState(() {
              _fetchingData.value = false;
              _error = error.toString();
            });
            return const SizedBox();
          },
          data: (data) {
            setState(() {
              _fetchingData.value = false;
            });
            return const SizedBox();
          },
        )
      ],
    );
  }
}
