import 'package:ajmal_now_doctor/features/authentication/domain/user/user.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/certificates/certificates.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/experience/experience.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'providers.freezed.dart';
part 'providers.g.dart';

@freezed
class ANProviderModel with _$ANProviderModel {
  const factory ANProviderModel({
    required String id,
    required String user,
    String? biography,
    required List<ANExperienceModel> experience,
    required List<ANCertificatesModel> certificates,
  }) = _ANProviderModel;

  factory ANProviderModel.fromJson(Map<String, Object?> json) =>
      _$ANProviderModelFromJson(json);
}

// Extension for user details
extension UserDetails on ANProviderModel {
  // These methods would be implemented if we had the full user object,
  // but for now we only have the ID reference
  String get userId => user;
}
