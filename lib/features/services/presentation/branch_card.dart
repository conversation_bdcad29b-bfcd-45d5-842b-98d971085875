import 'package:ajmal_now_doctor/common/general_widgets/rating_bar.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/features/authentication/data/auth_providers.dart';
import 'package:ajmal_now_doctor/features/statistics/domain/service/service.dart';
import 'package:ajmal_now_doctor/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_vector_icons/flutter_vector_icons.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

class ANBranchCard extends ConsumerWidget {
  final ANServiceModel serviceProvider;
  const ANBranchCard({Key? key, required this.serviceProvider})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authRepositoryProvider);

    return GestureDetector(
      onTap: () => context.pushNamed(AppRoute.serviceProviderDetailsScreen.name,
          pathParameters: {'id': serviceProvider.id}),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 6.0),
        decoration: BoxDecoration(
          borderRadius: circularBorderXXS,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Logo Section
              Container(
                width: 80.0,
                height: 80.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12.0),
                  color: AppColors.backgroundColor,
                  border: Border.all(
                    color: AppColors.primaryColor.withOpacity(0.1),
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12.0),
                  child: Image.network(
                    serviceProvider.logo?.url ?? '',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.business,
                            size: 32,
                            color: AppColors.primaryColor,
                          ),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      
                      double? value;
                      if (loadingProgress.expectedTotalBytes != null && 
                          loadingProgress.expectedTotalBytes! > 0) {
                        value = loadingProgress.cumulativeBytesLoaded / 
                                loadingProgress.expectedTotalBytes!;
                      }
                      
                      return Center(
                        child: CircularProgressIndicator(
                          value: value,
                          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                          strokeWidth: 2,
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              const SizedBox(width: 16.0),
              
              // Content Section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Branch Name
                    Text(
                      serviceProvider.name,
                      style: GoogleFonts.philosopher(
                        textStyle: TextSize.r.boldStyle,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 6.0),
                    
                    // Service Type
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                      child: Text(
                        serviceProvider.type.name,
                        style: TextSize.xxs.mediumStyle.copyWith(
                          color: AppColors.primaryColor,
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 8.0),
                    
                    // Rating
                    ANRatingBar(
                      initialRating: serviceProvider.rating == 0 ? 5 : serviceProvider.rating,
                      itemSize: 16.0,
                    ),
                    
                    const SizedBox(height: 8.0),
                    
                    // Location Information
                    Row(
                      children: [
                        Icon(
                          MaterialCommunityIcons.map_marker_outline,
                          size: 16,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4.0),
                        Expanded(
                          child: Text(
                            '${serviceProvider.location.city}, ${serviceProvider.location.district}',
                            style: TextSize.xxs.regularStyle.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    
                    // Phone/Hotline Information
                    if (serviceProvider.location.hotline.isNotEmpty) ...[
                      const SizedBox(height: 4.0),
                      Row(
                        children: [
                          Icon(
                            MaterialCommunityIcons.phone_outline,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4.0),
                          Expanded(
                            child: Text(
                              serviceProvider.location.hotline,
                              style: TextSize.xxs.regularStyle.copyWith(
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                    
                    // Owner Information
                    if (serviceProvider.owners.isNotEmpty) ...[
                      const SizedBox(height: 8.0),
                      Row(
                        children: [
                          Icon(
                            MaterialCommunityIcons.doctor,
                            size: 16,
                            color: AppColors.primaryColor,
                          ),
                          const SizedBox(width: 4.0),
                          Expanded(
                            child: Text(
                              serviceProvider.owners.map((owner) =>
                              // TODO: check logic that user in provider is string not user object
                              //   '${owner.user.firstName} ${owner.user.lastName}').join(', '),
                                '${owner.user}').join(', '),
                              style: TextSize.xxs.regularStyle.copyWith(
                                color: AppColors.primaryColor,
                                fontStyle: FontStyle.italic,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              
              // Action Icon
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
