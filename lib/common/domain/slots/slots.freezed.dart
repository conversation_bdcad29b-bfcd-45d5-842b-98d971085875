// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'slots.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ANSlotsModel _$ANSlotsModelFromJson(Map<String, dynamic> json) {
  return _ANSlotsModel.fromJson(json);
}

/// @nodoc
mixin _$ANSlotsModel {
  String get id => throw _privateConstructorUsedError;
  DateTime get starts => throw _privateConstructorUsedError;
  DateTime? get ends => throw _privateConstructorUsedError;

  /// Serializes this ANSlotsModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ANSlotsModelCopyWith<ANSlotsModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ANSlotsModelCopyWith<$Res> {
  factory $ANSlotsModelCopyWith(
          ANSlotsModel value, $Res Function(ANSlotsModel) then) =
      _$ANSlotsModelCopyWithImpl<$Res, ANSlotsModel>;
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class _$ANSlotsModelCopyWithImpl<$Res, $Val extends ANSlotsModel>
    implements $ANSlotsModelCopyWith<$Res> {
  _$ANSlotsModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _value.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _value.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ANSlotsModelImplCopyWith<$Res>
    implements $ANSlotsModelCopyWith<$Res> {
  factory _$$ANSlotsModelImplCopyWith(
          _$ANSlotsModelImpl value, $Res Function(_$ANSlotsModelImpl) then) =
      __$$ANSlotsModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String id, DateTime starts, DateTime? ends});
}

/// @nodoc
class __$$ANSlotsModelImplCopyWithImpl<$Res>
    extends _$ANSlotsModelCopyWithImpl<$Res, _$ANSlotsModelImpl>
    implements _$$ANSlotsModelImplCopyWith<$Res> {
  __$$ANSlotsModelImplCopyWithImpl(
      _$ANSlotsModelImpl _value, $Res Function(_$ANSlotsModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? starts = null,
    Object? ends = freezed,
  }) {
    return _then(_$ANSlotsModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      starts: null == starts
          ? _value.starts
          : starts // ignore: cast_nullable_to_non_nullable
              as DateTime,
      ends: freezed == ends
          ? _value.ends
          : ends // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ANSlotsModelImpl implements _ANSlotsModel {
  const _$ANSlotsModelImpl({required this.id, required this.starts, this.ends});

  factory _$ANSlotsModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ANSlotsModelImplFromJson(json);

  @override
  final String id;
  @override
  final DateTime starts;
  @override
  final DateTime? ends;

  @override
  String toString() {
    return 'ANSlotsModel(id: $id, starts: $starts, ends: $ends)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ANSlotsModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.starts, starts) || other.starts == starts) &&
            (identical(other.ends, ends) || other.ends == ends));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, starts, ends);

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ANSlotsModelImplCopyWith<_$ANSlotsModelImpl> get copyWith =>
      __$$ANSlotsModelImplCopyWithImpl<_$ANSlotsModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ANSlotsModelImplToJson(
      this,
    );
  }
}

abstract class _ANSlotsModel implements ANSlotsModel {
  const factory _ANSlotsModel(
      {required final String id,
      required final DateTime starts,
      final DateTime? ends}) = _$ANSlotsModelImpl;

  factory _ANSlotsModel.fromJson(Map<String, dynamic> json) =
      _$ANSlotsModelImpl.fromJson;

  @override
  String get id;
  @override
  DateTime get starts;
  @override
  DateTime? get ends;

  /// Create a copy of ANSlotsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ANSlotsModelImplCopyWith<_$ANSlotsModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
