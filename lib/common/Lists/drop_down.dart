import 'package:ajmal_now_doctor/constants/colors.dart';
import 'package:ajmal_now_doctor/constants/dimensions.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

class ANDropDown<T> extends StatelessWidget {
  ANDropDown({
    super.key,
    this.validator,
    this.value,
    this.hint,
    this.hintText,
    this.labelText,
    this.labelStyle,
    this.hintStyle,
    this.items,
    this.onChanged,
    this.selectedItemBuilder,
    this.dropdownKey,
  });
  String? Function(T?)? validator;
  Key? dropdownKey;
  T? value;
  Widget? hint;
  String? hintText;
  String? labelText;
  TextStyle? labelStyle;
  TextStyle? hintStyle;
  List<DropdownMenuItem<T>>? items;
  void Function(T?)? onChanged;
  List<Widget> Function(BuildContext)? selectedItemBuilder;

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField2<T>(
      key: dropdownKey,
      decoration: InputDecoration(
        labelText: labelText,
        labelStyle: labelStyle,
        hintText: hintText,
        hintStyle: hintStyle,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: AppColors.primaryColor,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(
            color: Colors.red,
          ),
        ),
        contentPadding: const EdgeInsets.all(1),
      ),

      value: value,
      validator: validator,
      // hint: hint,
      buttonStyleData: ButtonStyleData(
        height: 60,
        decoration: BoxDecoration(
          borderRadius: buttonBorderRadius,
          color: Colors.white,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        width: MediaQuery.of(context).size.width,
        elevation: 0,
      ),
      dropdownStyleData: DropdownStyleData(
        decoration: BoxDecoration(
          borderRadius: buttonBorderRadius,
        ),
        maxHeight: 300,
        padding: EdgeInsets.zero,
      ),
      menuItemStyleData: const MenuItemStyleData(
        overlayColor: MaterialStatePropertyAll(AppColors.primaryColor),
      ),
      selectedItemBuilder: selectedItemBuilder,
      items: items,
      onChanged: onChanged,
    );
  }
}
