{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98823aca29a29b41c0c0bd2b89f85b3d42", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9825851726cf3d6ce2e5560b4ce3b935b0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dccc03c44843dc66f174400928ebdd24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b3066faacb5b8c1ed124804730547b6b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dccc03c44843dc66f174400928ebdd24", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d574a2b4c6002940c1d4bfbc47a7e9f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c225096fcdbbe376d0a574241238292c", "guid": "bfdfe7dc352907fc980b868725387e985a5641404bde0288d113e85d0b89322a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98049e0ff4d5985f7e19c6aa8e8a2353e4", "guid": "bfdfe7dc352907fc980b868725387e9811fbe2d7f2c4d4a6be0d0aa3149b1569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4031093b098bd8a7dbea31041541baa", "guid": "bfdfe7dc352907fc980b868725387e98b172383abe01b836225b807603d0292b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b266f8cb80d1f3839f181598b57b5028", "guid": "bfdfe7dc352907fc980b868725387e98705c32334891d0b8524479f482d24411"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807f96754e4ea3720c1d2520131ab14ac", "guid": "bfdfe7dc352907fc980b868725387e9885c600bee9e2220381c2b8266eb08b60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2b1eabed6dfc450a4f09f48466d5d44", "guid": "bfdfe7dc352907fc980b868725387e9827003a470ce48a4bf05fffcb4631d58c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98770692f590a45555c3ccceea095948c2", "guid": "bfdfe7dc352907fc980b868725387e9807ede7676caef6865bd93aa06f88a2a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9fc08159e9709b3855522f715ca4dd0", "guid": "bfdfe7dc352907fc980b868725387e98232e46f8d7df1bbd1a8a76428664168b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980beaed3123f5eaae83abeaad08dbb073", "guid": "bfdfe7dc352907fc980b868725387e986729d9c3512a4268529e806db329ff02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4e3470c9646b1bcab49287bd2add10a", "guid": "bfdfe7dc352907fc980b868725387e986577741c3f793e55f52afbadf20809cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ed67be05be7f23d02617fad3d66f51b", "guid": "bfdfe7dc352907fc980b868725387e989561a7d3df13f9a349717cd7c6f3bc01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98320c0be94903b45e74017822260c01ec", "guid": "bfdfe7dc352907fc980b868725387e985803c4b4aaf4e353f43e444610c901af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98548a970ed547cf4c936324f659d59677", "guid": "bfdfe7dc352907fc980b868725387e98bd5b6fc91ede9b723db712b564446a5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e97f02ae1ca9b89a0be7da0abae16c", "guid": "bfdfe7dc352907fc980b868725387e98665eb98043efcaeb6b0d019395db2651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1a7b0a0ac2610481fb6a0ba13484227", "guid": "bfdfe7dc352907fc980b868725387e9832172794e7a27996599bd49c228ace0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c792648decf493454ce51edb3707085d", "guid": "bfdfe7dc352907fc980b868725387e98c7efda68bfb7ead34a67cfcd091f1be6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a0305a65cae5dfc3f4425e26a08f06f", "guid": "bfdfe7dc352907fc980b868725387e98ba638937e98214a8c730d35e1e8a7f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0c2c49d106cd72b2002153a6256d372", "guid": "bfdfe7dc352907fc980b868725387e98db4bee5603050172ade97b96cc15b6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93d7bf628ec51e6d71f30a99e1b4541", "guid": "bfdfe7dc352907fc980b868725387e98d490496298d2bf7dd1a3994469d2ab49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f50243c956286c7af8969f3816497e9", "guid": "bfdfe7dc352907fc980b868725387e9800fd50c4b9214cfbcfe8c4afffe3e723"}], "guid": "bfdfe7dc352907fc980b868725387e98debfd819404fddbe6684dff14cd47dc5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ee6ceb444d1efd7eeb1a04535564c72", "guid": "bfdfe7dc352907fc980b868725387e98ea59b141959c513c2a1a57a025808b9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7df9a6433fdb444c682085e5340903d", "guid": "bfdfe7dc352907fc980b868725387e98172c65f5e570a74285facd8ff81fc0a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e129174411397f3b55e2fa836a431566", "guid": "bfdfe7dc352907fc980b868725387e98b020cbee78c177e03745701f18fd3ba3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aed7fb0bdb1429bb442067e6a29895a8", "guid": "bfdfe7dc352907fc980b868725387e9828b5573fe88c32211bfc86302181e9b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e63919b1c3bbd76afd4cbf8e498d3db", "guid": "bfdfe7dc352907fc980b868725387e9841680ed6ca6eef81d30a757e04a22b83"}], "guid": "bfdfe7dc352907fc980b868725387e9858638e316364f45ed3bb6694dbb27b39", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98118d9d29c0ad18760b671d7023ba149e"}], "guid": "bfdfe7dc352907fc980b868725387e98fae3f6f9321e583865292619ab867190", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d8dc60e088d2922f674c6b7bee73b1a9", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e9838cd8307d9f7325c44aea1acac782b24", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}