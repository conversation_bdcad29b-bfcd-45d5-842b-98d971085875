{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce0034518c39001932eba60d9fd60eec", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986cda2705f2a11ff5cadbc5e3bb7c8afb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cde89137998ac7d3230ec9c1bf1b71c0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988d4fe56138fa04f35c2b4eacdd0316d2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cde89137998ac7d3230ec9c1bf1b71c0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896f0e8f2a84517a043f6f57e3f56ff97", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ba8c56e7e79e253b03b779822d602996", "guid": "bfdfe7dc352907fc980b868725387e98773e4795d402e58f17ad4227e9e173e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b8f7e2d5d12799c8d42ea784de49062", "guid": "bfdfe7dc352907fc980b868725387e986b205e313a019816b2cf1afba70d3e24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98752bf9999dd432d1f88b9e844e5cb212", "guid": "bfdfe7dc352907fc980b868725387e98f3418d5afaa1674204699ee8cb907e8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df332f100ffbc78524e37ea9967ec3b", "guid": "bfdfe7dc352907fc980b868725387e985e2ad9fd7687c5c23e430934054d6816", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca56f9448ee75a9ea4e9f35d5fb83ef2", "guid": "bfdfe7dc352907fc980b868725387e98770cb39ce6b7aacc17bb451c56f6726d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f92e55b7b958ee66f9bef8bb15642b2", "guid": "bfdfe7dc352907fc980b868725387e98d1541954e6dfc74ec4d3a4f0eb8809c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb09f8f534981dd5c267f0ae978dd1b4", "guid": "bfdfe7dc352907fc980b868725387e9880181014b1f01260270f4c4383cced17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984595c8554d2c39030b8c423d0d7617f1", "guid": "bfdfe7dc352907fc980b868725387e98d9623ec6d86cc80c516202ed783695d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d3bbf0bf130b5599d9c2d3d35720d71", "guid": "bfdfe7dc352907fc980b868725387e989a722c5313e47959e2d5456b27b8471e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987047a7e3dc859f4a03bd98fd02b8fec5", "guid": "bfdfe7dc352907fc980b868725387e981eaf739820f381694b1809dc8c378768", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986567da19acf19c134ab286ffa0d6770f", "guid": "bfdfe7dc352907fc980b868725387e980e015b0ab9b3f761bc4637541687e0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98692262f725330b61af2be9c4e960ce80", "guid": "bfdfe7dc352907fc980b868725387e980e1989743e54c2f653a8c13d148c3042", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08dc065a80f81829ac02844fa6a1564", "guid": "bfdfe7dc352907fc980b868725387e9828c8b75e7b18faedff884389822feeb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f80575a572574133c20fc22f1c96bfe", "guid": "bfdfe7dc352907fc980b868725387e98b7646ad613050652ecd46e6de1ab1eaa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a90a5742ac146374813f220a066e4cc", "guid": "bfdfe7dc352907fc980b868725387e98b181ae2ff6e3f4e419d96ef271434bc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840b428cc6914b159290f5ee5414a06e4", "guid": "bfdfe7dc352907fc980b868725387e98d50cec15b0c62f773f575bec58445d07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a39f4c89615da6dcf03026e81ece36", "guid": "bfdfe7dc352907fc980b868725387e989b60949c1fe8a519b080adf970c61f7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5fe5085e091c9a8f38ccb3c8b722516", "guid": "bfdfe7dc352907fc980b868725387e98cc9f282c58b0139106247c57a88b4333", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4786033736b23c7bb4b2361742f53c4", "guid": "bfdfe7dc352907fc980b868725387e98c76e8b55244e88e673c57f1af972eeed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d9b9efd3faeda082ac9e5723a2e2b62", "guid": "bfdfe7dc352907fc980b868725387e981d14665023c22101c0f35d0a57e77cf3", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989498ab0e85d58c7cc9b4236cd3b75fec", "guid": "bfdfe7dc352907fc980b868725387e9839beb44849a1e59aaaf7ffa7e09b90c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cdf790acd770bde8331f30cedc315e3", "guid": "bfdfe7dc352907fc980b868725387e98f78445b915ddc098b2dd045ee5496fb4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d1b783c634cbc691b4cbd3f17057512a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9874f229c87d5faa087f3a16e11133b9e2", "guid": "bfdfe7dc352907fc980b868725387e989d2f727b7cc2363a9d0d6e1ef6d4253c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df7fe6996c6e714a6f5c24db7d845029", "guid": "bfdfe7dc352907fc980b868725387e98469d2030cac122280ad562249b19696b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9171136bb9f5dd376a05fd840e5a75", "guid": "bfdfe7dc352907fc980b868725387e98226f21bb222ee3719c296ca95fe29832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b5e295b08060774e88cb323b0798e4", "guid": "bfdfe7dc352907fc980b868725387e98423ee65b9d50f0f7a9ce1e96096b5672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ffc1c501fdc565baccb4d071e91c136", "guid": "bfdfe7dc352907fc980b868725387e9849b3a76a7a4b51349d01a4d4e6b26d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf14f2377aa3fe3370b9da285340766", "guid": "bfdfe7dc352907fc980b868725387e981d6fdb38227e8103a31cf6484ad338bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c531bb1584b66649ee27f5f1183ee9a", "guid": "bfdfe7dc352907fc980b868725387e98b7f1414420372ac2250ca0f0352fa1af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c3c974822114992e59b9757817b50c4", "guid": "bfdfe7dc352907fc980b868725387e983d58bb86f5f17b1f85525d7aabc4f163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3469487f1c013bbbf67e8d5a6b40df0", "guid": "bfdfe7dc352907fc980b868725387e98623ff30a9804557157d68694b29c2c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad30edaf9a4036356357d57606f41bad", "guid": "bfdfe7dc352907fc980b868725387e982eb486685273b7e4ecac0765c2d6272f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f05d424ea4012f615dc53acd1c059b8", "guid": "bfdfe7dc352907fc980b868725387e98488196f165a073f461b7af2d25745bff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016fd148355c494031d734852b8f116e", "guid": "bfdfe7dc352907fc980b868725387e98a0a70427f26e822fd4de8bacb58fda03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98824e04fcc5fbc67f95391e1498c2cd48", "guid": "bfdfe7dc352907fc980b868725387e988d771ec233d24a309451c59d3fd4b854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813bb53e3103ac407c8e519fdcd1d538e", "guid": "bfdfe7dc352907fc980b868725387e9865b18ab8a29ce98147285b07a862111c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db31e5cabb8325ad5f85601589039885", "guid": "bfdfe7dc352907fc980b868725387e98021dde444cf56fc45eb2e846a631c881"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c088f932c6f1298fc65a8b0399fd3b35", "guid": "bfdfe7dc352907fc980b868725387e988cfff2fb6757f36cb39c8b2442dd0807"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986aa27a2d14ab7cd0e0f57884035c489d", "guid": "bfdfe7dc352907fc980b868725387e987141ae145a7c23904c5f5bf056d31089"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98246adcf7a6d2ec458f4eda7c394217ab", "guid": "bfdfe7dc352907fc980b868725387e982bff0e525cd691640b2ed9ac8b93d76d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eb91600d3e687a33bd3a489ec669a69", "guid": "bfdfe7dc352907fc980b868725387e980e637828a2dd210049cab8718be171f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0eae0d13627944d1c86efe3230873d0", "guid": "bfdfe7dc352907fc980b868725387e98e9b4810cd69c1e0b1984292f30505071"}], "guid": "bfdfe7dc352907fc980b868725387e988b0c4402f96eeefb237214a7a2d90034", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98a4245002136cae8a7d9cce594b1a2622"}], "guid": "bfdfe7dc352907fc980b868725387e9873041e5e37fec7e7bb7e0d1d391e27aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986a2d05cdef4d7337f2c4b9062706ec3f", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e989d44fb4ea89d692e6074c2da80bb6f37", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}