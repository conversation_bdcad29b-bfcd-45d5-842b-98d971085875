{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886efe451edce27fd5d205655146b950d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981189a5d4f231c7ece204bed118829fe6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885d7c6588fb7bd8d93e034213350a348", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ac9a164d711f5381bbc09a47ca0d337", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885d7c6588fb7bd8d93e034213350a348", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af466662a1a9738b94b0eb2bedf50e3d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e329c2bc9461266a040a2c41e84e1ba", "guid": "bfdfe7dc352907fc980b868725387e9805dba3fe6c685a5af8285cd8385530cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cde56ce131c59f76b7d66ad0d7825c1", "guid": "bfdfe7dc352907fc980b868725387e98852507be6be2fc99694a5c28a015c6e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811b6845f6f6eac29f3a3ea773ff736c6", "guid": "bfdfe7dc352907fc980b868725387e9896b68038d219e403332be2d2625d5721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efca32711e8554111ca4439e6780041c", "guid": "bfdfe7dc352907fc980b868725387e98aec9bab8c95e4f95824f64cdeeda2318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7a46958adb284b8486eb21dd30f4d1b", "guid": "bfdfe7dc352907fc980b868725387e987c2b819520b52e6033dcb323d8f30a51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7834f8989bb6677d945671fb5096fb", "guid": "bfdfe7dc352907fc980b868725387e9898e00603e5e8a74d9c489c04942a2896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b33ff355986f085a96fa3239563c99ed", "guid": "bfdfe7dc352907fc980b868725387e984548f5c08b85e4f952ed58c22b85c627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889abd67db6222af30c81ca68308c75ec", "guid": "bfdfe7dc352907fc980b868725387e989ae5dedcfebffd464eb2d314b817f699"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878ac83b36b4e6521bddd936a18eb1248", "guid": "bfdfe7dc352907fc980b868725387e98e95f7622e2b566b5219bc8e47a3e445a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6a8a77fc0087544b17f2f0696671a4", "guid": "bfdfe7dc352907fc980b868725387e985dfdda9eb187756798111074ac8d01a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cbfc465a10e060c2da09804ebd36e24", "guid": "bfdfe7dc352907fc980b868725387e989eac540442d505433ed934b38db88928", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aec3a72ffc15dfbf53b5d155cafc66c3", "guid": "bfdfe7dc352907fc980b868725387e9862cb31f02c2bd499b9f58793a9d74fd1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7908d371fad8ef2dea6694832f5ce0b", "guid": "bfdfe7dc352907fc980b868725387e98d7238010c2c9651b8ea71cd0bb87199e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98907f454a7030f991be658b01efbe1fa5", "guid": "bfdfe7dc352907fc980b868725387e98e5d8649d55dd19ba104317890ddc78c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6eb9b83c0ce2deae3e0d8a265bbfb31", "guid": "bfdfe7dc352907fc980b868725387e98263360ec26ecaa14c7d116fe03da8d79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b92174fe9a41dd5090474aabd3bf25f", "guid": "bfdfe7dc352907fc980b868725387e98f43cd359937268103d07889be6ce3a97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bee9a09ca92627012d266082a42f18cf", "guid": "bfdfe7dc352907fc980b868725387e98147081217d3bbca517d8c60873edfe37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86697d1b82266982dfb97afe241b3cc", "guid": "bfdfe7dc352907fc980b868725387e9824096306704d13334bb1354005f4634c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844bee5dafcf04260b9c17fc5e5e9085a", "guid": "bfdfe7dc352907fc980b868725387e98a711a857b0d026ddb63fa6826cacd77d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b463b85d64c7b7820ea47c673d47392", "guid": "bfdfe7dc352907fc980b868725387e985f7e8e10440fff3076ee024ad43823de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae212f29f25e3d014d6742be41ef5837", "guid": "bfdfe7dc352907fc980b868725387e98f532b53a1c56d6d79db6617807012a47", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831ca066d7535f9ab4ce9af6e0d220cfb", "guid": "bfdfe7dc352907fc980b868725387e984abc56188c8ad3f05d210e98e9547789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f261a086dcefd463188314c90bd68b9e", "guid": "bfdfe7dc352907fc980b868725387e9870a857d6d812a29a89617130765286d6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98de3edb99a2bdbd6ee1d34675f1c0429a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e37635fdcfc44f17a7ce785d45792e7f", "guid": "bfdfe7dc352907fc980b868725387e98d09718ff3ed1c6942e5c2b25c3c51799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22336ad32c75f0b518f73ccd9e4a36a", "guid": "bfdfe7dc352907fc980b868725387e98ebc4ec9b61d4a0ca6660521450cf8b29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825417946e9a53b2fa5403e59cf5e1c63", "guid": "bfdfe7dc352907fc980b868725387e9829d7fb1b327e033a7fa35accc240e660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4717f9cb774f9f758d5051497d20924", "guid": "bfdfe7dc352907fc980b868725387e98804067c373ddf82522052b554894920a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987608735fda0db2ca372858b0f837fe01", "guid": "bfdfe7dc352907fc980b868725387e9867c366614d38d07145e44b4feffbad03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e82b1d2829d9f5490cf60aafc99469", "guid": "bfdfe7dc352907fc980b868725387e9852b8a5e2f0ef9364d7039785f4615eb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804deeaea469c301408bba1291ecc7d30", "guid": "bfdfe7dc352907fc980b868725387e98f9a14e62e2a94e0b8db19333900543bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e98d32224b3e357fed506bd6ef27acac", "guid": "bfdfe7dc352907fc980b868725387e98a0aad552bb41469be7e318227ae56fb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b04433a4c75dbbb868072d5558bef11e", "guid": "bfdfe7dc352907fc980b868725387e980b9e3a70a7f374ade3784586de08cab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b626284557fc18ef180128402a34cd55", "guid": "bfdfe7dc352907fc980b868725387e98c6c694127acc484c311afc97f3131d04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fe085e6d7ef9ecd3e3d9120bd65b10c", "guid": "bfdfe7dc352907fc980b868725387e981fb29300708f1e18bc33bc4d329bcf5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98176df3b06012114e43422926768d4e06", "guid": "bfdfe7dc352907fc980b868725387e98762fb7df95587e17461e5e69fcfa78ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f26d30da6555052b48f852d6656880", "guid": "bfdfe7dc352907fc980b868725387e988904a820b55adabafbbf935cc11b4da3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987770cd7c069a805781de01d09aebab90", "guid": "bfdfe7dc352907fc980b868725387e987277cd8456c03e462520fcc178781a10"}], "guid": "bfdfe7dc352907fc980b868725387e985be37916457c621d9bd777a67d3b9be6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98dc5e5d7ccadcc8a37cf3c263fe79a0b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c79e3a47b0fd16d57bfa9ba1a3978209", "guid": "bfdfe7dc352907fc980b868725387e98dfb5b793abc8f8425b506014c0149b1a"}], "guid": "bfdfe7dc352907fc980b868725387e98fa7c425b8f3a747809dd8361d82a51d2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9812608ace34eeaebfa7caebf7a5458ff2", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98977224a9302fe4987c8b07e43c53a8ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}