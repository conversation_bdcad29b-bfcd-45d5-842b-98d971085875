{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987685cfd66c0e17d3809e787c71cdee15", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892e3aee34d475a9e3e846a5aff1c4b2f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808e6c883db6be052ec471d7e240cbc13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845d3536009864e9117c86a2b6d16a4dc", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9808e6c883db6be052ec471d7e240cbc13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980be39529b32eaea9cf15f8ae4fc07f1a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c5c6665e278daa75e6608c711a81a141", "guid": "bfdfe7dc352907fc980b868725387e980286d5f5c97b0782df011a592100089c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c982f3d13769524421cfd328c2fe4f8d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b62fe779c5d1da8354fcd99c15dcc2b3", "guid": "bfdfe7dc352907fc980b868725387e987b78fe09f3e8a6480b504e22be8f2d12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e512643ae5880391047ad0618daf9cea", "guid": "bfdfe7dc352907fc980b868725387e98ed2dbbc293a3978edfa890885bebb0c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98572ecf90f70243235d9c2a7df22800a0", "guid": "bfdfe7dc352907fc980b868725387e98cf02ef5bde956d26e01a176eda1ff775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a6ae7415eb38b093a9b6580582a181", "guid": "bfdfe7dc352907fc980b868725387e9808ed2081305e17ecb6363bf93e7b4d9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a08ece23a9d3a1d898440e66e497fce", "guid": "bfdfe7dc352907fc980b868725387e98e3f55718400ef2146eef089005929041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fc4a18e713e32d465299990ddf04d7c", "guid": "bfdfe7dc352907fc980b868725387e98f62d382651c9018ce6db895bc5e31ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bf5529fddb5682a576d53bf04ec39a", "guid": "bfdfe7dc352907fc980b868725387e9811e3441da139dd7baa59f2dafdd5e989"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0933a797174261a551177302a3d7539", "guid": "bfdfe7dc352907fc980b868725387e985f9e285dbac06f904a875cb637856d28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869f76a4cfcc0030810e33a9ba33933e8", "guid": "bfdfe7dc352907fc980b868725387e9804372b3fe3e65e5bf15d9d1c1fd05939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c3906b4dbee998e75d1618202ed9624", "guid": "bfdfe7dc352907fc980b868725387e985f378605c9a4b09f744280911e9a227a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64bad661f6c1f0a801e74b6428f1e25", "guid": "bfdfe7dc352907fc980b868725387e98a13b64fddef32d642f53e1839f92834d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c589ef1538c8dcf946524cf73a4d13f", "guid": "bfdfe7dc352907fc980b868725387e9813ecbd3545c937c4f7680b7392529504"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848135165f0e6f82ae0a216eb1012e982", "guid": "bfdfe7dc352907fc980b868725387e98807a1743a328f81ed0e4b3ff77b13fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98186e540f2b198848a08b79231ac65fa1", "guid": "bfdfe7dc352907fc980b868725387e986461963940e5b0b5538f4888972be042"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98944617048ad955fb01345a00be77c513", "guid": "bfdfe7dc352907fc980b868725387e9862e980cf5f4183ed8b5d5eb26a8cc60b"}], "guid": "bfdfe7dc352907fc980b868725387e9893732b0177d1ffb0c8840a7414a0584c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98a20d17f879afdcca99d44d4efc54c945"}], "guid": "bfdfe7dc352907fc980b868725387e9883441a86e8ec4cf14249494d7d861d9f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983fd42b903abafb5e830d2552310c3ea3", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98c6a226ba5f2917f16b39928196a80661", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}