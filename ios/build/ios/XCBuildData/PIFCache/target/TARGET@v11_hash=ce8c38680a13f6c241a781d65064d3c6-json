{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9819048dfa8ec633e9d06b34bae8b64737", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f57634461bfc07541306e0e0909c0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0aeffbde0bb2317ed3592256a4d5f72", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b77ea053d833db80a19dea3c0cec528e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0aeffbde0bb2317ed3592256a4d5f72", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fcf27a1859dbb654e6d1632ecd3da9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98666e3b5a5dd9f2fc55fa83bd44896e19", "guid": "bfdfe7dc352907fc980b868725387e98ba5a719f03637779d0e4114c173859a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98454ffb633c5bcc183c41415f8b0dd788", "guid": "bfdfe7dc352907fc980b868725387e98e2307c7e3b6ededb064e5ddd2a68ec15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69b7da393c964b4b7a4d61cfeec8bb7", "guid": "bfdfe7dc352907fc980b868725387e982d562db17f8b5b4b452d2545362214e8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863e822dde12559f52f1ae671b2cb7cc9", "guid": "bfdfe7dc352907fc980b868725387e98d04ba8a39c5cad90f8f9d0692576eaa8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d619cde3a5315a2f2a259d5c2eebb004", "guid": "bfdfe7dc352907fc980b868725387e98c33f235531156ee0adb6b8d65a5c7830", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aabaf5f975b60f89d92b11deae936dc5", "guid": "bfdfe7dc352907fc980b868725387e985a895af414c847d231b5f4da7f1e0eb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a7c361b6c0ddbb25df49d2580d7143", "guid": "bfdfe7dc352907fc980b868725387e98a1f1dfde0a897a7a48297b39703ffe87", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7a4576cfad3218683dcebbba77b581c", "guid": "bfdfe7dc352907fc980b868725387e98cf56166b008f01c301c175ad0b214b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc96a4a45bac81cd1ed4b61557edcd88", "guid": "bfdfe7dc352907fc980b868725387e98d42fe72f176a80ac9bc8f49ca458e7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70cfaf2bc9c64280880954a9f24c87e", "guid": "bfdfe7dc352907fc980b868725387e983f53c7c613094709f31b06d10211e623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf00122cd02a00ecea25db3f6c85c186", "guid": "bfdfe7dc352907fc980b868725387e982529dcf37e031935f7df29cb7a0c72be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed271f1b0963dcd32e33ea93fda14fb", "guid": "bfdfe7dc352907fc980b868725387e98b957187b0e56ff1342b0658816919310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ba3f43a6b1bc74eb3d7e18592d16f6", "guid": "bfdfe7dc352907fc980b868725387e98d1e1455bdecb88b6dd994789b1a8d005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7aececce3a9cd16b3c7046933b0a457", "guid": "bfdfe7dc352907fc980b868725387e984dc9a9173228d460dce8bd05febcaa21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a51e86a0abd1fe36774f77900fd64d14", "guid": "bfdfe7dc352907fc980b868725387e9866e18cdc65ffb51bafc9666d133d6961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbae99a67a78b222b5aca6b8de17ff32", "guid": "bfdfe7dc352907fc980b868725387e9853fba8ec8f30e77eea0ee42d162d7b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cda1e569aabac1eaafbe8fcd37289fe", "guid": "bfdfe7dc352907fc980b868725387e9850835d8912cad2b1f1810e1d3a3f127e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a8aa0e5dc098ad14ddb6fc4fefd1130", "guid": "bfdfe7dc352907fc980b868725387e98625e8428f767980263004f084f2f06fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fb3e756fa8c11f81d32701737125830", "guid": "bfdfe7dc352907fc980b868725387e98c30c5ac2b99a509847068097d1b90b04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa8831b85e1ecf976976b4d858b76b0", "guid": "bfdfe7dc352907fc980b868725387e98d90e924c70c687d95153625ccf22e1b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836a2081adbc8385e3ca55444cbc4c87d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984617da4bc9dfa6c59c8a3301dc5e1671", "guid": "bfdfe7dc352907fc980b868725387e986b7cfccea424129119a7e0b7223d203f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acff3a2c8509383df45574cb0ccc6207", "guid": "bfdfe7dc352907fc980b868725387e987e2fd0030cc6bcc3a49952734b8e1436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855297a9909c7617b5b45f37ceb23f881", "guid": "bfdfe7dc352907fc980b868725387e989addc411376632e0842314e1f3dbd3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d7ac777fe9c1f9998dc8ea41331f09e", "guid": "bfdfe7dc352907fc980b868725387e98f2b75083ed4223737365239d09129b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04f6af84d1e2a90c3c00b903ae33494", "guid": "bfdfe7dc352907fc980b868725387e98f6a65e94218fb5f98f3b3395aafcb285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f65a6789e73ddc62ccbf72693dc67b1", "guid": "bfdfe7dc352907fc980b868725387e98719f6caf81038a4fb6b36c401e41927f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e509856ae446df3647d4a154030c2466", "guid": "bfdfe7dc352907fc980b868725387e9897454b0f741f6ecb896065015ddc7084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985156f84e1bd3d1a1c775b6ed0f51d1c1", "guid": "bfdfe7dc352907fc980b868725387e980555b502c9c0197b86706e000f128fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa42e395d9d82f2a35b4cf768533cd52", "guid": "bfdfe7dc352907fc980b868725387e9803c38b008504781c8e28657eb9ba9929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86c2597652239cdd4d6a484782f938a", "guid": "bfdfe7dc352907fc980b868725387e985a2ed91aac5da7e9139f8ae19e8ee587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05fa953a27f5373a2ad86a34308bb2e", "guid": "bfdfe7dc352907fc980b868725387e988ad01aaa6d9324c69a1230aa30684633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab77d8ed9e3abf0a05a7b18c75663e81", "guid": "bfdfe7dc352907fc980b868725387e9871bc7f2e968b7164541a22f887513a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe3b7e3d2094c461badfcdd00b3c794", "guid": "bfdfe7dc352907fc980b868725387e98811905a9c4ba0e19039cca05ddec61d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98563e4e507dc4b3be886659766e7a0359", "guid": "bfdfe7dc352907fc980b868725387e9833f6e0f517dacc5b6398ab313854ccb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf2afdaf3c21d2bfe1afb168b5751d0f", "guid": "bfdfe7dc352907fc980b868725387e980bb1e730b18176ad52f058cae5417ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2808b3b13e772faf388fb308a3b8452", "guid": "bfdfe7dc352907fc980b868725387e981af43685ae73254d59946e622f376749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8eb97e44931dc2ba2d1d444f43e99aa", "guid": "bfdfe7dc352907fc980b868725387e98641fcdc650b720a8dfa8fb181fa118f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dde1f6d525a703b7caf328c78bea57", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e9844ec617da2f257b0b49d378a54c7b385"}], "guid": "bfdfe7dc352907fc980b868725387e987ca18eb2083c48f78e919a58dc322457", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c95c6b31cc49dcf5ba698c2dfa2917e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}