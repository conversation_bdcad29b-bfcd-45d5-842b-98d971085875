{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9897d2daa86a913f6c0a49f219bc7fee73", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810acb479fcccd8bd664134a15619688e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810acb479fcccd8bd664134a15619688e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989c9dfdba1b72caa39167a00fb159156c", "guid": "bfdfe7dc352907fc980b868725387e9817011278146d850ad8c1800732eb1e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbbd2ac7a37be280a8e2fa4d8213192", "guid": "bfdfe7dc352907fc980b868725387e980ac0e33a793cc19ff4abfb357eae768f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaa436c1634637ca592704b3875807d4", "guid": "bfdfe7dc352907fc980b868725387e989d6f395bb26cbecbfd60c1405ceff351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a1535bac42980559bc5c4c1b0b32e2", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ac95092130d3feefee6017b76bafe3", "guid": "bfdfe7dc352907fc980b868725387e984a42ac0fe50661fb4253f6986ea45bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98554bc4a6757a30ff9fae8ddcf384602f", "guid": "bfdfe7dc352907fc980b868725387e98de0db65b473b31d0081f4a5269702284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982158cee13fcee3d972c7f1e49a9b73d6", "guid": "bfdfe7dc352907fc980b868725387e9860e7129d76385a521f0b9fd136393b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985858180ad1e83c94275643ea6652e21f", "guid": "bfdfe7dc352907fc980b868725387e98c102fd841861eee3b0b8bacef085b917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c489c2338594f5c7a9a386ab35bb40", "guid": "bfdfe7dc352907fc980b868725387e98f8633a22aa08547c812ac9bec8b70ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98494a09ffba8c9986b743bdd92dc3c607", "guid": "bfdfe7dc352907fc980b868725387e98daf47c85e530f3405587d1d40166baa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98929e9ed69f6fffc1eba817aeb8699bf7", "guid": "bfdfe7dc352907fc980b868725387e98acadf1494a3e3cd73f3caf0731d75f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b9b2514dfcc103ddf4d8ce30541136", "guid": "bfdfe7dc352907fc980b868725387e98cdf6a5c0e94262f9f4ac605bffef3bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980354985782d539d7b1bc741f7fa093bc", "guid": "bfdfe7dc352907fc980b868725387e98749c0e50cc6ab0c5d2ae99062d78bd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824296f268d2a7550fe5fe76f0a1f1bd0", "guid": "bfdfe7dc352907fc980b868725387e98b60c9e9460d623e196c3542325e313b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988df50bba65bac821e31ae35e7debec1c", "guid": "bfdfe7dc352907fc980b868725387e984176d1afb1f259dfdc07efe17ea652cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ba0707daa6448d9f2744aa582da0ba", "guid": "bfdfe7dc352907fc980b868725387e98bc8f2f12d75589e115d9449c5986f9fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ed8f899b4cc81d4de0dd6e439335df", "guid": "bfdfe7dc352907fc980b868725387e9818b5f752d3526a5d027411efb16100bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f166c094999976dad407a38a118f2d1f", "guid": "bfdfe7dc352907fc980b868725387e989de49e9d16c76d6cdb2cd05defe828b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e0b75291227910f99110073dc360c5", "guid": "bfdfe7dc352907fc980b868725387e980869c8041a3db4283bedf358c81f931e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806f38cf6ebc77f058827050e59933c06", "guid": "bfdfe7dc352907fc980b868725387e98c1f1e163902f92d3f444c8eef23fe9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c57ef6f3eb02cfd2c5a849b85624ec", "guid": "bfdfe7dc352907fc980b868725387e98ae14d59b03d674cb5e1bad0f3d076dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f29b36970976707e4bdbcf30bd59da3", "guid": "bfdfe7dc352907fc980b868725387e98edc2a89fb1a6e200ab1412ec87f9c9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0d8da857a51d4b71342b6c1a8b4152d", "guid": "bfdfe7dc352907fc980b868725387e98492b5866dd16c57c285b5e79c6867b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bc7345aa3c955b00e10d0ac40dc28d6", "guid": "bfdfe7dc352907fc980b868725387e98e484c4f5150fbf8fabdad69f35a52c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873e12c7f3629438e55bfe9912c855a4b", "guid": "bfdfe7dc352907fc980b868725387e98d15e2dd2c99fea1c18b18e69ffd1b703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855b9e351d8835bfdeba253b4a931e923", "guid": "bfdfe7dc352907fc980b868725387e986fee5e9c3343f8d1008352e701bc900e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c02821766cc19606a5e78fdef606989", "guid": "bfdfe7dc352907fc980b868725387e98a3b33d70365c2c21c6e5ce5c403a6767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831a9011143ef4dfdb6cedab7e96cd643", "guid": "bfdfe7dc352907fc980b868725387e98bc874e43a4cb78d8f4d1a0ac40c942bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980032b93db012eb9fe8f24754bd0a2553", "guid": "bfdfe7dc352907fc980b868725387e9829d2b0b2b7d95a9ddf7e598d6ffc4db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2da11de92e5af0010938e115c9902c5", "guid": "bfdfe7dc352907fc980b868725387e988ad79f08695ea6c2cca3c5d7d8b8e33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885d39cafa9f9402f3b307818967b69af", "guid": "bfdfe7dc352907fc980b868725387e98eb8f6a719f355b17f23d8eb2e9a5d44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c382ab80d2a3c99e799a3e9bf521ae4c", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98950921a21d877f797dcad84fb7819cd0", "guid": "bfdfe7dc352907fc980b868725387e98838df245c5366c7ccd43779dddceac8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98778c23ac750461a269b34cac162d814d", "guid": "bfdfe7dc352907fc980b868725387e980cc73214077d885d0ddd9bbb802b0c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8e9a02bd3f94b61cf8527bc0247444b", "guid": "bfdfe7dc352907fc980b868725387e987699d61d47db16d864283d0af14a2a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2e714861aae8b1ab4e3209f13d9f93", "guid": "bfdfe7dc352907fc980b868725387e98e97804a71a7eda4782f6d18bbb55dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd4653decd0e38a9ee3bed4905d4c8ca", "guid": "bfdfe7dc352907fc980b868725387e98d47beba43c550f9d5c74eaccd5ff08b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eefd32f5f84656f82260816baa11da8", "guid": "bfdfe7dc352907fc980b868725387e982130a66579435704115f7e00d09f53d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6886aa1e169e76fb8d0bb4d4f735d6", "guid": "bfdfe7dc352907fc980b868725387e98163ea20656902ae2aebec0d2b7d2c959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980accc9343474ebf870371cba404fd4eb", "guid": "bfdfe7dc352907fc980b868725387e98c065af2abb2799503e76158497948974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98552dc8fbbf1c478785e52290a345faf4", "guid": "bfdfe7dc352907fc980b868725387e986c074fcd043656e86edc370b80f2b6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838e93f1a92ca002c2fd4a82945f4944d", "guid": "bfdfe7dc352907fc980b868725387e98897022fb7c83452617bb5421eee661b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b440ef71f5381886ce1fe8816f265df", "guid": "bfdfe7dc352907fc980b868725387e98cfe537ef6fd48aebe0e7b866f6b1142d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e2611711ff0dddd9b76a43e0d4a9218", "guid": "bfdfe7dc352907fc980b868725387e988830eb10a635306371e79be09704e0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985151b6976ae0ec33ea4cc226fc1a87b2", "guid": "bfdfe7dc352907fc980b868725387e989ac43435114fcf14579f257bc3f9c364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad7e447758874e9c9d7ae98a8b6aeed2", "guid": "bfdfe7dc352907fc980b868725387e985cf8026ab77fe57445a9e11bc4c4c7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e4b11344e7f75fa06784213eb42816", "guid": "bfdfe7dc352907fc980b868725387e98ef071ab9cef1d242de5b1113119d33b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c0ec5e0087485cec6e184b9e958026d", "guid": "bfdfe7dc352907fc980b868725387e9852ddaf4c3962f25802ecf6e4d216c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e2a6c8bf40a046a439e59a247f0a46f", "guid": "bfdfe7dc352907fc980b868725387e98abfa7315b06c10451197abd58e79d711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a121fc1f0c96ce4dcd0a61b2a3e55455", "guid": "bfdfe7dc352907fc980b868725387e987f00ce95bbef4bcae9c2b6d4ab613e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831736232c04b7fbd71a07edf15ce6a6d", "guid": "bfdfe7dc352907fc980b868725387e981243c2de66d0689b6cc84852675e392f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981aed5a7eca698a52d9b329f26d1f14e0", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e7eb44ba9c0b257d1bf51c2035a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6845fa19d750dea74f4281aa5e8cf55", "guid": "bfdfe7dc352907fc980b868725387e987e1026d7e32253d02e2a13c87719a604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b33fd28592f2f98fc788a338939817", "guid": "bfdfe7dc352907fc980b868725387e98fe57562c583a8a215f5783496b1ef801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98891aa784a47ad0e5f30b9ce3e66cbc54", "guid": "bfdfe7dc352907fc980b868725387e98aa36efef915c33b7edff59f6a9a0bbfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b941ed493ec04bc4cf2e04c8aeda03f", "guid": "bfdfe7dc352907fc980b868725387e98fd0b1420ef9ecbffd05e08127fc813c3"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}