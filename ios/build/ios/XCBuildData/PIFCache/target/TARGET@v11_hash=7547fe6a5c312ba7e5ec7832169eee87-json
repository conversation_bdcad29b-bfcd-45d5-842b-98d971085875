{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c0fc156eca20caad6039b9a3f8a2174", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807088b8ce50677a0e24968c834423c86", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807088b8ce50677a0e24968c834423c86", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877b50d4c5fb015607664ff8ac9aa9c85", "guid": "bfdfe7dc352907fc980b868725387e985f0085899a9f5aec5a0202605a8918e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98166703dba4af542e96f84b417a23b61f", "guid": "bfdfe7dc352907fc980b868725387e98611718009e578d911dfa24820f8938c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98950c10beb5679c965a4473afc99cf7b3", "guid": "bfdfe7dc352907fc980b868725387e98a6682a128e26f73988588a0b9dc99c4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982436cc63e264716bdd86b0cb6cf2281d", "guid": "bfdfe7dc352907fc980b868725387e98a3fcd6fa162c0d365d634616e789d6ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c9d8586389ba76123d4005115ce51b", "guid": "bfdfe7dc352907fc980b868725387e983e6167a4e115668fb3799c465a0cfc40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf3f30b43c83eec8f6e3f5d6c7cdcc6f", "guid": "bfdfe7dc352907fc980b868725387e98322b1742f5524a6d0e37f0b0497cc76c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d843a79de16dcbb930b9969d19f877", "guid": "bfdfe7dc352907fc980b868725387e9843ada8db56df3fa463ceac3d83ee854b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a549593d4787dfb5fcd243df8e3a1ea", "guid": "bfdfe7dc352907fc980b868725387e980ac4bf4b39ebe460e5e8c74c9450f291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988eb362ada91df970d867bc892a1845aa", "guid": "bfdfe7dc352907fc980b868725387e98bbd8a28e44ee02016bb2b7154fb70861", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33aec0cae55ea1272888c963c2efaf6", "guid": "bfdfe7dc352907fc980b868725387e9843fad2db3368a046c718d5256715c904", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c85819fe627a58472f28b3722e4e919", "guid": "bfdfe7dc352907fc980b868725387e9828d12d05508c1581ba2436be33f4de22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df1b52a25413a2e28dbbfefe63040211", "guid": "bfdfe7dc352907fc980b868725387e982a76ee02c6f40170d9e26dcda3f47392", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c76dc9b048d220df3e6cf16aeda1b53", "guid": "bfdfe7dc352907fc980b868725387e982179a03359c5c1547754ab9df73dc6ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ab0388bb9fb463e5398aede9ef9188", "guid": "bfdfe7dc352907fc980b868725387e98d678334a5d238374f2ad9c04b29d4364", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667e19e806534110f7d3c5f81bdfb01a", "guid": "bfdfe7dc352907fc980b868725387e98775d03fe3e3492d0099115e1bf707a2c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2d13f441125eda9453d1b4701148b5", "guid": "bfdfe7dc352907fc980b868725387e98079c00739040ad2f95375b026b07ccfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f923838edd89f3050ebaa54652d48c", "guid": "bfdfe7dc352907fc980b868725387e98e3932ebd3a8b73ab380edcbb9178253b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d268bba186f0cddf9b00e0457e6cdff1", "guid": "bfdfe7dc352907fc980b868725387e98a280d73544069688727cb901c0e4c23b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d16231d850d96e69f5ad82645970466d", "guid": "bfdfe7dc352907fc980b868725387e98b0f7618034ed189c15db88fd3fea3d89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a79625e7c82019d5249d4350cb6ca93", "guid": "bfdfe7dc352907fc980b868725387e98c601d2214f03bd2cd74b3097ecac9d80", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d9532f214da4333fb3fbe82c9906898", "guid": "bfdfe7dc352907fc980b868725387e98adadec3b201175c8d0e30e0e0875c721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e121c780da919e12f063c3fd4ddc44b1", "guid": "bfdfe7dc352907fc980b868725387e9855bbf862dbacc561aea6d6c3b71beffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e603bca512aa1fab6a1331e84e02a7d9", "guid": "bfdfe7dc352907fc980b868725387e980933bd8b7809f3d220559f518c73495b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faf5af3db0c19dd9af951b41e6eff61d", "guid": "bfdfe7dc352907fc980b868725387e984f3927b5025fbab50f810894b2944645"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cea2a08c33fc6db80d9f43564365b3ba", "guid": "bfdfe7dc352907fc980b868725387e98a7337d83be2cab6dc31cc46cebb38081"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848479db2de3a55498b8e1da531a571be", "guid": "bfdfe7dc352907fc980b868725387e98c97a8ecbd5b2762aca0093db40716ef3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864fd9ca8f94bf38b3d8819ad5b79f40b", "guid": "bfdfe7dc352907fc980b868725387e98ec11d162d18201d0db7fc2a6c6450791"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1ef484a0b18285dc6cb96a53a885b02", "guid": "bfdfe7dc352907fc980b868725387e98b89d056539b9db589f8f435fb0ee22cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc2ac4fb0bf3bbb18643a2d8fc9e0fe", "guid": "bfdfe7dc352907fc980b868725387e98e74e91e4c474192ff4f6c671250611a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840b3e96d1c9a997806ee9906742e5b01", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f689bc3693a23a4a272a608fcc8bec1", "guid": "bfdfe7dc352907fc980b868725387e98f3c9db90a8372e8e2e2d76a4d3f5b63f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e621fbdea227c9f0cf15bf8bb7299f3a", "guid": "bfdfe7dc352907fc980b868725387e98ac4a1bc695ff6aa06833c9cbb5c44312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e2b9e0f8e30808e6a5d7b94547de1d", "guid": "bfdfe7dc352907fc980b868725387e984219fb5f96a080c36f13a083e2af6774"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c536a19b4391b96f934fe4dcbab4ec", "guid": "bfdfe7dc352907fc980b868725387e98ad13bb31d800eda1d0c0c6976c915b1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aa1d752fc56df12e8d02368bdf5acff", "guid": "bfdfe7dc352907fc980b868725387e98fdd592debdee1d55b93b79d74af0e92b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee5ad66ff98d24106ff9aff34ed7e0ed", "guid": "bfdfe7dc352907fc980b868725387e98cf4f6f22792b8db021e8e1c754326233"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}