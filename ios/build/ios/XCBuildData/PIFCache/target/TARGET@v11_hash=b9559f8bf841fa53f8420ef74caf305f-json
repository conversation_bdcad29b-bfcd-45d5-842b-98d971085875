{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873a6d5379c9a17d949f9c50d160ed5dd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980ac77992fb7dc72566572960116ffdda", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bbee729a62188a9eccaa02c13b6ea00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98895ebdfb213ad41f1db4035420c955c3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bbee729a62188a9eccaa02c13b6ea00", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ed72bc1f021a905d248bd86db6b18a1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9878125ca7422a94abe15f68db2d6fe9da", "guid": "bfdfe7dc352907fc980b868725387e98791ffe0f8ad946b2ba83a0153fc69131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef514ad22c9853ca7f55be4f444ff851", "guid": "bfdfe7dc352907fc980b868725387e98312d0ceba28a02a1f5b3c01a8b2f826e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987500d23991f4a75a6ab8eaa5fe348321", "guid": "bfdfe7dc352907fc980b868725387e98a14871b18fa9b8fa6c5a89bdc798009b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e0676ef702beb4ec00be1cd541ef600", "guid": "bfdfe7dc352907fc980b868725387e98939231b9001e0dfe46723517cc1b92a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beae0e22ec7b72db7cadaa666ec84239", "guid": "bfdfe7dc352907fc980b868725387e9837e827a74293971ec7afc29368dd3020"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bb97f367c0d948400203568588e06ee", "guid": "bfdfe7dc352907fc980b868725387e98cd57c0b5716e085e66fc936bf9306515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee2772df5456dadca46a5373835904b4", "guid": "bfdfe7dc352907fc980b868725387e98c4b84d0ff352b39f58aea36bb53411fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98846a9780f125442ec463b126c1155ad6", "guid": "bfdfe7dc352907fc980b868725387e983fa55188adb9a8a3638bce3b392828a9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831871cae9a13802a2d2773a9a90c2458", "guid": "bfdfe7dc352907fc980b868725387e9831c4ae7e01ef6fb657d47655632ec36c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b27395092f7cdbeb7f0f2234ff222e3", "guid": "bfdfe7dc352907fc980b868725387e98e1d587d800cdbfa1905446c4133f590b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981769b2c39ff82eaf6704126aac3f3c0e", "guid": "bfdfe7dc352907fc980b868725387e9897ed5b71fddd6a621756c078d04cdd38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622dbdec51a2b2944aa38ba13d4263ec", "guid": "bfdfe7dc352907fc980b868725387e98422d516ee60d5b7f9aee3be144d149dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab41e22a9232340f71a53a1da6b8dc3e", "guid": "bfdfe7dc352907fc980b868725387e98c007074850df16416e789da09b427fc7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0e8f5cebbe23cd4ac78295999f12f95", "guid": "bfdfe7dc352907fc980b868725387e98c7b2bf1f8b77c0ee3de1dc11e13660d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995b698950f74fba3966e88f7284427e", "guid": "bfdfe7dc352907fc980b868725387e9805628d5265ce7f3b077c895b15d5518b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584a4d37b24ed20fe80bfda61b2b7996", "guid": "bfdfe7dc352907fc980b868725387e9895f24aa77dc684d81f16d2b416f26bb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845440802edfbef045e929235773a2fa3", "guid": "bfdfe7dc352907fc980b868725387e98f679f7c7a5fec3535f5ee52e5430de12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c85b5031f2888c66a9aefed961227c9", "guid": "bfdfe7dc352907fc980b868725387e986ccb9d1e4b923cdd019f5295a179a4b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c868da609d8100684360fac2d2ec3d", "guid": "bfdfe7dc352907fc980b868725387e98d88679358b7b60ba970a5bce78068e63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984955feb3c84f6988f68b3a782bbfc966", "guid": "bfdfe7dc352907fc980b868725387e98fb0f91f96420050373726e5dfdf9e0b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf0a79bacf4588467c225c6fa7690ad9", "guid": "bfdfe7dc352907fc980b868725387e984e14b2e36640d1bd7b41cc03fc30b1c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986065b1ddd6de1695b3bace0abbe940dc", "guid": "bfdfe7dc352907fc980b868725387e9865590b46ebcab5b616f8177183306701"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a521d2dd252a0b83387d9abc6fa2452", "guid": "bfdfe7dc352907fc980b868725387e98c9353f79542e02d825586ae24ee3c3ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8979a2671a49b66f2d2858a35bd408a", "guid": "bfdfe7dc352907fc980b868725387e98a1369d3bbcbe4a960fb4b43f7090b7e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f428c2cc2a2be7b2ea700b8cb05fdb07", "guid": "bfdfe7dc352907fc980b868725387e98d9e5dfa6466c955d5c0a77408c5582e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877262aad65707b1877ce09b5cc46734a", "guid": "bfdfe7dc352907fc980b868725387e980d2e35dde608ae17d387931ce0f51a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867e34aeaec9fa547d606ecd119e7ee5a", "guid": "bfdfe7dc352907fc980b868725387e981afd850bf1dfc6e2ebe8cbadb86e5859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb80ba44f6798cad6c09961a0d21dbf", "guid": "bfdfe7dc352907fc980b868725387e988c7663143fa2aae69272b63d3bfb98b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988269c7160163f80af9f136bf25f8650d", "guid": "bfdfe7dc352907fc980b868725387e98de2e316bb0245ac72715c4c43b8b784f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e7e9fcf29e8a2b48f64b0f7c8be2b4", "guid": "bfdfe7dc352907fc980b868725387e9882d0892865ecd989546346ea6a04b4d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886591158e85848ac2ddbd5ab8b6e1db7", "guid": "bfdfe7dc352907fc980b868725387e9865830f5b5543f2ebaed3df9d0b10e7d1"}], "guid": "bfdfe7dc352907fc980b868725387e98abd98dd1eec5765aa2a3db6b3f47821b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eb495f96836826a51943ac0b41ea4620", "guid": "bfdfe7dc352907fc980b868725387e98faf4267f84562d3e8a3df8125ad06120"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873cf7be0018153d7683f1c8be366633d", "guid": "bfdfe7dc352907fc980b868725387e9832a0c5cad9d33650bd484d7584a3fb72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885003a7d93b086977a190438781b7b8a", "guid": "bfdfe7dc352907fc980b868725387e98dcc9a45ff62aa29324d2fbd534eef536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db1461bfac83897cde455f5ba059d9c0", "guid": "bfdfe7dc352907fc980b868725387e98bc41c16fe4cddabc3aa3588686930666"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98164525cb38666962a61f3b877f7705f7", "guid": "bfdfe7dc352907fc980b868725387e98a19da71289b49893603f599a235c60b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321067251775f923c6c3d1e0f4b75f7a", "guid": "bfdfe7dc352907fc980b868725387e984361e8757d5c33f0bcb853fa3391c14b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f7626b098bce9761bc92fcb4e4f1cb1", "guid": "bfdfe7dc352907fc980b868725387e98c53257aa1429f4d3d5b6ef0a6e450829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a3154fe79e84c020dfcbf2c6ee9d2ed", "guid": "bfdfe7dc352907fc980b868725387e982f00d333df35e030c37d04cc36197a5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d18fc254676c4b82b5b5dfad2ff5df02", "guid": "bfdfe7dc352907fc980b868725387e98ff75c07ff58ed80847039f9ac73b2df5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b60f107e3206551423e3f8f11b9098ef", "guid": "bfdfe7dc352907fc980b868725387e982067f9ee6a09ed2f5fbad8f97a930220"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef8091d4bf2e1ef32e2c86407351930", "guid": "bfdfe7dc352907fc980b868725387e98b278fcb6586c84330d8e680b9926649f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e55dca67378d22553bfe91c5c6787e8", "guid": "bfdfe7dc352907fc980b868725387e9836b81547aa3dbfef5361c20f43df7141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5684e7840aaed2caf04ea2a21c409fc", "guid": "bfdfe7dc352907fc980b868725387e9849ce9a694402410443215406796824e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4daa56f691aa0bcaf3f826a164d280", "guid": "bfdfe7dc352907fc980b868725387e9817c3a68f18baebf0d0d78dfe1eee6572"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fbab9be02e7bd540234b16d6f6075da", "guid": "bfdfe7dc352907fc980b868725387e9813c07cf40a5b5911e56e23cf53e12375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2f7181f60ef83e1bcb9f6309daaa547", "guid": "bfdfe7dc352907fc980b868725387e98f947e203bea1f58deaa3b7fb26f4e6fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae1d4914137807f18edf996ea6b7dae", "guid": "bfdfe7dc352907fc980b868725387e983a43da81a0c215c58315a73304dd353d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edabfa0d24e00d5d9e25da7c0bd2c45d", "guid": "bfdfe7dc352907fc980b868725387e988a005c12074b5d3281ce8a376fc4f953"}], "guid": "bfdfe7dc352907fc980b868725387e989460533772497605449e39b31990eb92", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e9842c28d9329ed2b490e9a5ce744a8376d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98098627df1b719b39cc607b6f4fd7263f", "guid": "bfdfe7dc352907fc980b868725387e985f8167d45701bc5c6b1e51f28c461895"}], "guid": "bfdfe7dc352907fc980b868725387e982229b7d17d9fb87e2a4d8e83f15be13f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d2a5c44e4fe5dc847cc615331bce7bde", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98ffca97190530fae89b816d3822d851d0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}