{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9891ead8b03140969087a1dc35ae49df7e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c98696a020b77b2e43166487bc994c7e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f35926c75b49da4e7f08f768f95477b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae9d36096fd79710b5b7c9ff6a61522b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f35926c75b49da4e7f08f768f95477b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f59536783381b97fc93d4152a2baf4d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983879001bbd041bdf5c12ae7170380721", "guid": "bfdfe7dc352907fc980b868725387e98581ed96c44cd55b9673e67bcd96ac42b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811763901365d2af09baf7af429d0eb59", "guid": "bfdfe7dc352907fc980b868725387e983b3ae9330f571bcdc8ecc32fd4e3db58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8692738c042e84b6ce27d3a08b45113", "guid": "bfdfe7dc352907fc980b868725387e98697c3086702d31ef266daa8fdd38ae25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef77f6e6f15bc0c4686fffd7f0f070b1", "guid": "bfdfe7dc352907fc980b868725387e98418612ef6c6b1064a99d94b6b1b26373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f44a80ace86c197ed53fa28674657b67", "guid": "bfdfe7dc352907fc980b868725387e9822d76285abac64a0a11797b9dd708e97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c9f52c94a4b8a9e222eb8899642872e", "guid": "bfdfe7dc352907fc980b868725387e9835a00c255bb4567956e84a3c2c8db685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5186dabe6f36ae3516373273a137d4", "guid": "bfdfe7dc352907fc980b868725387e985f1646815a030b398085d8ebf593c87e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879859785944377cb32f534501b77d288", "guid": "bfdfe7dc352907fc980b868725387e98fa935b451bd84a09d3288355ff96ca10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d75bde953715cdc27447041e65dd316a", "guid": "bfdfe7dc352907fc980b868725387e98783a1f117069b37e55b65f2a837c4928"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986015ea2de051e9d4ef289daef064e77d", "guid": "bfdfe7dc352907fc980b868725387e98d0c4dce378d70f9253498a02b415bca6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d97fc745490afea754fc7ac30ffac8df", "guid": "bfdfe7dc352907fc980b868725387e988c1d281928723563f00ab55975dbec7d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c650b11e5ed58bce4d67e2fc975e45", "guid": "bfdfe7dc352907fc980b868725387e981540b01c91cb052f8e933dd0a533aaba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fa7f917affab5312d5dcd07d473d328", "guid": "bfdfe7dc352907fc980b868725387e98633d13aaf703034522122d57b67e483e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874408ae5b3c0cb23b6d5404475d5b8c0", "guid": "bfdfe7dc352907fc980b868725387e98a1dd8d2714e2a5a096c9c89541a8ff3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98344f4277ca1c92359622ee6b7de166d4", "guid": "bfdfe7dc352907fc980b868725387e98029d1f5a4189550d41536a8cd95ecd96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cafe5cd8812eb24ee8124fc96a90cfd", "guid": "bfdfe7dc352907fc980b868725387e98f2115482dd6b4aa9309be569d43ddf75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b56b2bf14e32958b76701057b5072f", "guid": "bfdfe7dc352907fc980b868725387e983cbfd9500e04a1df640fcb34e20e854e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf88e93b3de40b71f4f712e49a5aeac5", "guid": "bfdfe7dc352907fc980b868725387e98e9842e8b87fe0ec2a5a98b572255d564", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed191729bad7ad70a74d3a78e998d6e7", "guid": "bfdfe7dc352907fc980b868725387e984f0e57cfc2b5e0ecc196daebbcd9ff64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a3e23fe77a908a30ea3990cb9cf4153", "guid": "bfdfe7dc352907fc980b868725387e98d89a677584ac33097b20b2f4dfd42382"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825223a8ec3a2803a2453706342eb7c76", "guid": "bfdfe7dc352907fc980b868725387e98bdbbf8f9a1509e26a34af295d4e9119e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92c5115253c48aaa493ee9e7af11b6c", "guid": "bfdfe7dc352907fc980b868725387e98173881c36336d555de308e36002602f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981011564cccbf830509c8041d7ad9b88e", "guid": "bfdfe7dc352907fc980b868725387e9885b17e54fd081bf42e6d199730c1929a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989445009fe55eaaec08dead48e170a6e1", "guid": "bfdfe7dc352907fc980b868725387e9872f4ca0178241d0a22c1208a30f5786b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df7235a0b096af8cb91e333aa5ee1bd", "guid": "bfdfe7dc352907fc980b868725387e98758973a73415cbb64c535f75809d41db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e108eab019962db9efe59bbe3111c1fc", "guid": "bfdfe7dc352907fc980b868725387e98cfb76156ac321c0fb616a554328aa473"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981363b690a86ec10bc237f9109d467003", "guid": "bfdfe7dc352907fc980b868725387e98f73ead30782f1078fcc541bce6b64559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83981a29d2a1ccff9ac2dcfa5d2668c", "guid": "bfdfe7dc352907fc980b868725387e983e2fd72ed113c40d597ebeac6c90793a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c2d6fbc1b6b3d941adbdf49e39e504", "guid": "bfdfe7dc352907fc980b868725387e983df763353cb3a3e3b4b11cf457b271ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5c14b00de75f49ee6d013df829caed1", "guid": "bfdfe7dc352907fc980b868725387e986c1d874280d1ce450ce93f1f442d6fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed2f85b43b3f71264ddd78790f6417b", "guid": "bfdfe7dc352907fc980b868725387e9851ed557642106fa5cdd0b71b80150ff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f502b42de5278b9db19119fa46df916", "guid": "bfdfe7dc352907fc980b868725387e988badaa6b8ecc2caac3805024eabc78b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988511a0be40ffd8597a546208d5fba045", "guid": "bfdfe7dc352907fc980b868725387e987cf3c255486dfd4c712522675d670960"}], "guid": "bfdfe7dc352907fc980b868725387e98e4f28917fa30c85f5ba5fec07cfae220", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f46040cd29f0d3ab80883e11a209eeab", "guid": "bfdfe7dc352907fc980b868725387e98747dc51feca8a83921999a51c0471a45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989774ab86918a0dfabd39cd9ba1c11088", "guid": "bfdfe7dc352907fc980b868725387e9840d548938b1564b6483fc437cf25ad14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea67e527e0a539e6c1de8606792fa263", "guid": "bfdfe7dc352907fc980b868725387e98f34b337cca937260eec62508b51f8de6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f4cb1fcf422da0d4196e5c97d42900", "guid": "bfdfe7dc352907fc980b868725387e98b57cc5a47500b6f0459754829bd564ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b209f3838f47feb0090470b6b5d069ce", "guid": "bfdfe7dc352907fc980b868725387e98ac87d7fe236eba4ef51d5af47e5e2f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ea4eb1d8ee856d597018f6015bec0f", "guid": "bfdfe7dc352907fc980b868725387e98cc122ce183786f4c88f58d268f8ed946"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988075af81de2bb7e49202d1576abd8db1", "guid": "bfdfe7dc352907fc980b868725387e982ab51ad0bf75a28e8768c07827c2a1a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab961f0faa11a2478b14ebfad6a5eb59", "guid": "bfdfe7dc352907fc980b868725387e98f524e2d66b9b21cc805de6f6be4aa05b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e31e0a9c6f00dd5e0b5927dc783b10", "guid": "bfdfe7dc352907fc980b868725387e98c493ffa2aedc87092dd04495dce4475b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98538c2c21a3b21947f5691acaf7cce1de", "guid": "bfdfe7dc352907fc980b868725387e98ce700ff1cc239d24d521c411a1df9792"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bf765827f26736e851c8a039c550b47", "guid": "bfdfe7dc352907fc980b868725387e988a756c4aca1e656688dfbaaa950c1d33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987155e2d4bb56224825d6e952c0296b20", "guid": "bfdfe7dc352907fc980b868725387e98dc8ceb0a390ace0556f4a6c6f7dca74d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e52af7d597c0334104550a0c2cd9ab7", "guid": "bfdfe7dc352907fc980b868725387e98897a89840cae10b594ada6e9c887e519"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab31d58f4a2079eb80d00487099d48d", "guid": "bfdfe7dc352907fc980b868725387e98d36a0e09ec076aca60c450c20c06265e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859d9dbe448697947927ee122a7a93f7", "guid": "bfdfe7dc352907fc980b868725387e98ad1f57233b1d8493190e22c1f3ca7988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844b26f6f12831b67870971dacd4244fe", "guid": "bfdfe7dc352907fc980b868725387e98494b74f1354324bf26db82cbea9349eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0011df4f52af9a2a39c86fb55f24574", "guid": "bfdfe7dc352907fc980b868725387e98c57e8ef768521966fdfc0d3b488c80df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b81bf9ea2a29a04459800b10888ac2f", "guid": "bfdfe7dc352907fc980b868725387e98f3ab3d71684639fac9e3cdbde677cb6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9bde8b1de807d6920b8fe2ae1c3746e", "guid": "bfdfe7dc352907fc980b868725387e98e116a42001f820648a6c7c2668ecfcb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d61290afa0e301c19df50ee3ed6e9c2", "guid": "bfdfe7dc352907fc980b868725387e981f0f0551b21875207d33e63c17bdec23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98252748f6a42b65f632c80ea5b71df0f9", "guid": "bfdfe7dc352907fc980b868725387e9834d8b3c667ab44261eafea97bd17bd87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e8b3890d4099d3293c0f3a07215f38", "guid": "bfdfe7dc352907fc980b868725387e9899dca6dae55499e8c56931f78f6e0db9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2389e3efb350dedaa6ecd07c83d8193", "guid": "bfdfe7dc352907fc980b868725387e98895d6d4d82bbb5706763b5c75ef79f8c"}], "guid": "bfdfe7dc352907fc980b868725387e989e51aa2ec97e31ff7ed2782a011d892f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e98b81f146669219f848a40ef5d2942c54e"}], "guid": "bfdfe7dc352907fc980b868725387e988ae34578291af925e4e5d1def09971b4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9878024896e567d0979403000d5d01d43f", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e980592cb9b22a740b8afa16f33ac128798", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}