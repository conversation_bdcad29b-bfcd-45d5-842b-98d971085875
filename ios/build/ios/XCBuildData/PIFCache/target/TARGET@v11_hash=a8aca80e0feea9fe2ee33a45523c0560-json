{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ea4fa951d2c6078e17127b42cf1ef1c9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984060ba78e62b8720e1f65c57bb214304", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fdf49fbab249f42903613452f9b017de", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985eaf4f0985bb3ee8bd798a3a1092c615", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fdf49fbab249f42903613452f9b017de", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_NOTIFICATIONS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f04ffbfa365ceaa3e72163071642629", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9848ec08fb5907e95bc106c06ce71e24ed", "guid": "bfdfe7dc352907fc980b868725387e984470846589cdfc9678b128846a5c86ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8531441649a3705370231af5673b3a", "guid": "bfdfe7dc352907fc980b868725387e982fd7fbe14693003331dcd8bab9a4f82e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a6ec9d0d1e7fe1e4cfb124b076c74de", "guid": "bfdfe7dc352907fc980b868725387e984cfe7aaa1c8ef929e56512e67271a318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a621035a7291d84776373baf7702d099", "guid": "bfdfe7dc352907fc980b868725387e98acdb9a75c6269d48ba0d58430224f654", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98508b8ade7b86a76f51f3f53422c72c44", "guid": "bfdfe7dc352907fc980b868725387e9821416006db65a09fcbf2b437035e8829", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852902810a82dbae55cb7c15ada59033e", "guid": "bfdfe7dc352907fc980b868725387e987cb97cdc18d704b6b8c73134590f0552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2f2aa8b82e67e3e4034625264c1112", "guid": "bfdfe7dc352907fc980b868725387e98a740485fbcf62bb2d3fa08d48d55a3e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6de7d9920d40fcb7fc830ea73577f6", "guid": "bfdfe7dc352907fc980b868725387e98685cfa0421c26f33d90ecc8b8540942e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca7c50be4c9a7fae48050921cab3cdb5", "guid": "bfdfe7dc352907fc980b868725387e985b22830b2e8e255a538ca28d3ac8f99f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7bd7fa209345281c7d1a360dff0269f", "guid": "bfdfe7dc352907fc980b868725387e98e134a6df67d8fd87eece78cf5188e9a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d03d1c922300bf678cdc80c45ffe2a9", "guid": "bfdfe7dc352907fc980b868725387e9861411fa31c575d9fa0d094ce3d6f03b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d634cdc84d3ba587c109f708ecc0ffb", "guid": "bfdfe7dc352907fc980b868725387e982e36005a553744d015a779655aa512ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c8dd065be004c3e9f236e4e90f770d9", "guid": "bfdfe7dc352907fc980b868725387e984e628282d865e3a2d39d538ade9be30f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b714d3759ac8eee4506fef2903d99c23", "guid": "bfdfe7dc352907fc980b868725387e989c0eeb5ff8e0515be66cc42706adb388", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984030c3d51cc0ea494ea1c1d4ff568d98", "guid": "bfdfe7dc352907fc980b868725387e9825673c7f0664c1b44d4a6dc5ae75b135"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985849c9f5876a23ecd93f91012a3d6ae2", "guid": "bfdfe7dc352907fc980b868725387e9889de34f24fa09de188db82a080622ff0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98098a5ec65589af3fc120fc260bab3dfd", "guid": "bfdfe7dc352907fc980b868725387e98a23585f2e4c4d91d89012cb3f38c39ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d9b5946110d778dfb997d078df2708e", "guid": "bfdfe7dc352907fc980b868725387e98efe9f64b62f3ecd429c92fa6617039e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892948a60c32e3cff475c3ebb99b1ae04", "guid": "bfdfe7dc352907fc980b868725387e98216aa4e9f6c23273e980894196d7c006", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982472b4c698da708be62ab7343289c87b", "guid": "bfdfe7dc352907fc980b868725387e987da3d7104f0fc3a8820d0d6067eb31fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e8cea792dbb4e096fe1eea900f743e", "guid": "bfdfe7dc352907fc980b868725387e982486674d9daf7aff306dfcae07da62e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809a27e959cc2c69c10f74abe1d2ad458", "guid": "bfdfe7dc352907fc980b868725387e987ab4c2a8c0d94225e8740c6c814003d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f25d81c1d440fd048d5f0f75315c7a2", "guid": "bfdfe7dc352907fc980b868725387e9863991b0fb3a9309d48f6542aa87a4a39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c23217e01001ed7bddc28b22672099d", "guid": "bfdfe7dc352907fc980b868725387e98ec344116bdb43c222deaccfd7ba2e865", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856739d8d6fd7960ccf84bd91d2280619", "guid": "bfdfe7dc352907fc980b868725387e9820d35c9b276ff8566a62479333d7484f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98977859dced383c0d9bc2cc4d34b0c296", "guid": "bfdfe7dc352907fc980b868725387e98f80f08582c48f6a29c63c215e136f06e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818478f90c302e4ba094d922f376faf16", "guid": "bfdfe7dc352907fc980b868725387e98baa5cdf4b67a90bc6b2a2348ccaed943", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a8d6adcdbc08ed23ea99bfbbb46a1ab", "guid": "bfdfe7dc352907fc980b868725387e9814be9af86594a5e5e05089b89320edbb"}], "guid": "bfdfe7dc352907fc980b868725387e98aba998fd2869121530fdc2d59c75c046", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f6a7bb89e6f820c027405266fdde087", "guid": "bfdfe7dc352907fc980b868725387e986d922a6554f29087f68cbfe1b1633d44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4767d68d64c7308d25fad0624f538d2", "guid": "bfdfe7dc352907fc980b868725387e9869a496ccbf916d64adb866382f9c9235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869bfc97de8c193575bebfb05bd4b9648", "guid": "bfdfe7dc352907fc980b868725387e987beb493d70321f6fa3919632d0278edf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880be24aa8e811c3f4b854d4121a8b4b3", "guid": "bfdfe7dc352907fc980b868725387e98a321759f431818df2aa629d22eb8bf85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e82ca3471d5b5d8fff6e2a587f235822", "guid": "bfdfe7dc352907fc980b868725387e98478f608c85d8950811bc39d624781281"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae08df6adbe7367abc5985f1ea38e60", "guid": "bfdfe7dc352907fc980b868725387e98001786ad8479a0e842a0e3f76b7aaf91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e26b6e8d0aec4a556d3c2f1b687b2a", "guid": "bfdfe7dc352907fc980b868725387e98dbcfa9f348e6f874820edffca29fa91b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98004695c58eb4a36f22785bd41e4be616", "guid": "bfdfe7dc352907fc980b868725387e98364c8984eff37c73f8a542db11406d81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a703bfd0ad1aded925215d63195b932", "guid": "bfdfe7dc352907fc980b868725387e9805c6e0d3c7d26077fcea370a60c27b01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7cd62367d64b5acfd9ff60942fd1f92", "guid": "bfdfe7dc352907fc980b868725387e988b3c686153293476e6071a02a5b96cb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d6e2425fc43d8757f25f2b6c4f4e13", "guid": "bfdfe7dc352907fc980b868725387e98a3b4094bc9137c60c51d57ff76f1f8b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e72bc5919cff0d0f43f55f0f535d78", "guid": "bfdfe7dc352907fc980b868725387e98afd2faa3ec53dd3ff6b4e6873007dae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f1a5a078c4b5beb3325e509c71f3d3", "guid": "bfdfe7dc352907fc980b868725387e98b37782dd90a8fd7f6c446a6311c90a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dba396ca5e11dce8461eacb1238f683", "guid": "bfdfe7dc352907fc980b868725387e9867153627a8a75d1860a3e1e6b0432f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3c190e73addb8be3e791a02d492882", "guid": "bfdfe7dc352907fc980b868725387e98d59b319986c5fcbb8f1a26aa79bf3473"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893eea31d4b92f823095d5f67e5d59d58", "guid": "bfdfe7dc352907fc980b868725387e98e69ac708d97df933332e73d3ebac5d85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509719557a32163cc51db5000d645aec", "guid": "bfdfe7dc352907fc980b868725387e98641910a000a55406eecd6e5f422a9add"}], "guid": "bfdfe7dc352907fc980b868725387e9874300b30552692510e24f126e512c91b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eba1c827bc821bf1c21565bce33525c5", "guid": "bfdfe7dc352907fc980b868725387e986ec662229f1ffe6dba4efb685db1dbe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98098627df1b719b39cc607b6f4fd7263f", "guid": "bfdfe7dc352907fc980b868725387e988285d625a1e880977958e1184f93a476"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb0e1a17dc237bd0a263f22815423701", "guid": "bfdfe7dc352907fc980b868725387e987f46df79cd13f8a67d8ed850963571c1"}], "guid": "bfdfe7dc352907fc980b868725387e98321b455dfdd2438355a2af0635d963dd", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98254644be3f7af59068f27eb9adcb145d", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98fc10865020c657559d9e4ee471310096", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}